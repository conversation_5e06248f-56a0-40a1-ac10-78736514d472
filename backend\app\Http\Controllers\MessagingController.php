<?php
namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Conversation;
use App\Models\ConversationParticipant;
use App\Models\Message;
use App\Models\MessageRead;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class MessagingController extends Controller
{
    /**
     * Retourne la liste des conversations pour l'utilisateur connecté
     * avec dernier message, participants (autres que l'utilisateur), et unreadCount.
     */
    public function index(Request $request)
    {
        $user = $request->user();
        $userId = $user->id;
        // adapte si tu utilises un champ different, e.g. $user->type
        $userType = $user->role ?? ($user->type ?? 'company');

        // Récupère les ids des conversations où l'utilisateur participe
        $convIds = ConversationParticipant::where('participant_id', $userId)
            ->where('participant_type', $userType)
            ->pluck('conversation_id')
            ->toArray();

        if (empty($convIds)) {
            return response()->json([]);
        }

        // Récupère dernier message par conversation et info
        $conversations = Conversation::with(['participants', 'lastMessage'])
            ->whereIn('id', $convIds)
            ->get()
            ->map(function($conv) use ($userId, $userType) {
                $last = $conv->lastMessage;
                // participants other than current user
                $others = $conv->participants->filter(function($p) use ($userId, $userType) {
                    return !($p->participant_id == $userId && $p->participant_type == $userType);
                })->values();

                // calcul unreadCount
                $unreadCount = Message::where('conversation_id', $conv->id)
                    ->whereDoesntHave('messageReads', function($q) use ($userId, $userType) {
                        $q->where('participant_id', $userId)
                          ->where('participant_type', $userType);
                    })->count();

                return [
                    'id' => $conv->id,
                    'participants' => $others,
                    'lastMessage' => $last->content ?? null,
                    'lastMessageTimestamp' => $last->sent_at ?? $conv->created_at,
                    'unreadCount' => $unreadCount
                ];
            });

        // trier par date du dernier message
        $sorted = $conversations->sortByDesc(function($c){
            return strtotime($c['lastMessageTimestamp'] ?? now());
        })->values();

        return response()->json($sorted);
    }

    /**
     * Affiche les messages d'une conversation (avec pagination si souhaité)
     */
    public function show(Request $request, $id)
    {
        $user = $request->user();
        $userId = $user->id;
        $userType = $user->role ?? ($user->type ?? 'company');

        // Vérifier que l'utilisateur fait partie de la conversation
        $isParticipant = ConversationParticipant::where('conversation_id', $id)
            ->where('participant_id', $userId)
            ->where('participant_type', $userType)
            ->exists();

        if (!$isParticipant) {
            return response()->json(['message' => 'Non autorisé'], 403);
        }

        $messages = Message::where('conversation_id', $id)
            ->orderBy('sent_at', 'asc')
            ->get();

        // Marquer read optionnel : on ne le fait pas automatiquement ici, tu peux appeler markAsRead séparément
        return response()->json($messages);
    }

    /**
     * Créer une conversation + participants
     * payload:
     *  participants: [
     *    { participant_id: 2, participant_type: 'company' },
     *    { participant_id: 5, participant_type: 'admin' }
     *  ]
     * Retourne la conversation créée.
     */
    public function store(Request $request)
    {
        $this->validate($request, [
            'participants' => 'required|array|min:2',
            'participants.*.participant_id' => 'required|integer',
            'participants.*.participant_type' => ['required', Rule::in(['admin','company','influencer'])]
        ]);

        DB::beginTransaction();
        try {
            $conv = Conversation::create(); // si tu as besoin d'autres champs, adapte
            $now = now();
            foreach ($request->participants as $p) {
                ConversationParticipant::create([
                    'conversation_id' => $conv->id,
                    'participant_id' => $p['participant_id'],
                    'participant_type' => $p['participant_type'],
                    'joined_at' => $now
                ]);
            }
            DB::commit();
            return response()->json($conv, 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['error' => 'Impossible de créer conversation', 'detail' => $e->getMessage()], 500);
        }
    }

    /**
     * Envoyer un message dans une conversation
     * payload: conversation_id, content
     */
    public function sendMessage(Request $request)
    {
        $this->validate($request, [
            'conversation_id' => 'required|integer|exists:conversations,id',
            'content' => 'required|string|max:5000'
        ]);

        $user = $request->user();
        $senderId = $user->id;
        $senderType = $user->role ?? ($user->type ?? 'company');

        // Vérifier que l'expéditeur est participant
        $isParticipant = ConversationParticipant::where('conversation_id', $request->conversation_id)
            ->where('participant_id', $senderId)
            ->where('participant_type', $senderType)
            ->exists();

        if (!$isParticipant) {
            return response()->json(['message' => 'Non autorisé à envoyer dans cette conversation'], 403);
        }

        $msg = Message::create([
            'conversation_id' => $request->conversation_id,
            'sender_id' => $senderId,
            'sender_type' => $senderType,
            'content' => $request->content,
            'sent_at' => now(),
            'status' => 'sent'
        ]);

        // Optional: create message_reads for sender (already read by himself)
        MessageRead::create([
            'message_id' => $msg->id,
            'participant_id' => $senderId,
            'participant_type' => $senderType,
            'read_at' => now()
        ]);

       
        // event(new \App\Events\MessageSent($msg));

        return response()->json($msg, 201);
    }

    /**
     * Marquer comme lu : on peut marquer tous les messages d'une conversation lus pour l'utilisateur.
     */
    public function markAsRead(Request $request, $conversationId)
    {
        $user = $request->user();
        $userId = $user->id;
        $userType = $user->role ?? ($user->type ?? 'company');

        // trouver messages non lus pour cet utilisateur
        $messages = Message::where('conversation_id', $conversationId)
            ->whereDoesntHave('messageReads', function($q) use ($userId, $userType) {
                $q->where('participant_id', $userId)
                  ->where('participant_type', $userType);
            })->get();

        foreach ($messages as $m) {
            MessageRead::create([
                'message_id' => $m->id,
                'participant_id' => $userId,
                'participant_type' => $userType,
                'read_at' => now()
            ]);
        }

        // Optionnel : mettre à jour status messages (ex: 'read' lorsqu'il est lu par tous)
        return response()->json(['ok' => true, 'marked' => $messages->count()]);
    }
}
