<?php
     use Illuminate\Database\Migrations\Migration;
     use Illuminate\Database\Schema\Blueprint;
     use Illuminate\Support\Facades\Schema;

     return new class extends Migration
     {
         public function up(): void
         {
             Schema::create('email_verifications', function (Blueprint $table) {
                 $table->id();
                 $table->string('email');
                 $table->string('code');
                 $table->timestamp('expires_at');
                 $table->json('register_data')->nullable();
                 $table->timestamps();
             });
         }

         public function down(): void
         {
             Schema::dropIfExists('email_verifications');
         }
     };