{"version": 3, "sources": ["../../../../../../node_modules/swiper/shared/swiper-core.mjs"], "sourcesContent": ["import { a as getWindow, g as getDocument } from './ssr-window.esm.mjs';\nimport { b as elementParents, q as elementStyle, e as elementChildren, a as setCSSProperty, h as elementOuterSize, r as elementNextAll, t as elementPrevAll, k as getTranslate, u as animateCSSModeScroll, n as nextTick, v as showWarning, c as createElement, w as elementIsChildOf, f as now, x as extend, i as elementIndex, y as deleteProps } from './utils.mjs';\nlet support;\nfunction calcSupport() {\n  const window = getWindow();\n  const document = getDocument();\n  return {\n    smoothScroll: document.documentElement && document.documentElement.style && 'scrollBehavior' in document.documentElement.style,\n    touch: !!('ontouchstart' in window || window.DocumentTouch && document instanceof window.DocumentTouch)\n  };\n}\nfunction getSupport() {\n  if (!support) {\n    support = calcSupport();\n  }\n  return support;\n}\nlet deviceCached;\nfunction calcDevice(_temp) {\n  let {\n    userAgent\n  } = _temp === void 0 ? {} : _temp;\n  const support = getSupport();\n  const window = getWindow();\n  const platform = window.navigator.platform;\n  const ua = userAgent || window.navigator.userAgent;\n  const device = {\n    ios: false,\n    android: false\n  };\n  const screenWidth = window.screen.width;\n  const screenHeight = window.screen.height;\n  const android = ua.match(/(Android);?[\\s\\/]+([\\d.]+)?/); // eslint-disable-line\n  let ipad = ua.match(/(iPad).*OS\\s([\\d_]+)/);\n  const ipod = ua.match(/(iPod)(.*OS\\s([\\d_]+))?/);\n  const iphone = !ipad && ua.match(/(iPhone\\sOS|iOS)\\s([\\d_]+)/);\n  const windows = platform === 'Win32';\n  let macos = platform === 'MacIntel';\n\n  // iPadOs 13 fix\n  const iPadScreens = ['1024x1366', '1366x1024', '834x1194', '1194x834', '834x1112', '1112x834', '768x1024', '1024x768', '820x1180', '1180x820', '810x1080', '1080x810'];\n  if (!ipad && macos && support.touch && iPadScreens.indexOf(`${screenWidth}x${screenHeight}`) >= 0) {\n    ipad = ua.match(/(Version)\\/([\\d.]+)/);\n    if (!ipad) ipad = [0, 1, '13_0_0'];\n    macos = false;\n  }\n\n  // Android\n  if (android && !windows) {\n    device.os = 'android';\n    device.android = true;\n  }\n  if (ipad || iphone || ipod) {\n    device.os = 'ios';\n    device.ios = true;\n  }\n\n  // Export object\n  return device;\n}\nfunction getDevice(overrides) {\n  if (overrides === void 0) {\n    overrides = {};\n  }\n  if (!deviceCached) {\n    deviceCached = calcDevice(overrides);\n  }\n  return deviceCached;\n}\nlet browser;\nfunction calcBrowser() {\n  const window = getWindow();\n  const device = getDevice();\n  let needPerspectiveFix = false;\n  function isSafari() {\n    const ua = window.navigator.userAgent.toLowerCase();\n    return ua.indexOf('safari') >= 0 && ua.indexOf('chrome') < 0 && ua.indexOf('android') < 0;\n  }\n  if (isSafari()) {\n    const ua = String(window.navigator.userAgent);\n    if (ua.includes('Version/')) {\n      const [major, minor] = ua.split('Version/')[1].split(' ')[0].split('.').map(num => Number(num));\n      needPerspectiveFix = major < 16 || major === 16 && minor < 2;\n    }\n  }\n  const isWebView = /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(window.navigator.userAgent);\n  const isSafariBrowser = isSafari();\n  const need3dFix = isSafariBrowser || isWebView && device.ios;\n  return {\n    isSafari: needPerspectiveFix || isSafariBrowser,\n    needPerspectiveFix,\n    need3dFix,\n    isWebView\n  };\n}\nfunction getBrowser() {\n  if (!browser) {\n    browser = calcBrowser();\n  }\n  return browser;\n}\nfunction Resize(_ref) {\n  let {\n    swiper,\n    on,\n    emit\n  } = _ref;\n  const window = getWindow();\n  let observer = null;\n  let animationFrame = null;\n  const resizeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('beforeResize');\n    emit('resize');\n  };\n  const createObserver = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    observer = new ResizeObserver(entries => {\n      animationFrame = window.requestAnimationFrame(() => {\n        const {\n          width,\n          height\n        } = swiper;\n        let newWidth = width;\n        let newHeight = height;\n        entries.forEach(_ref2 => {\n          let {\n            contentBoxSize,\n            contentRect,\n            target\n          } = _ref2;\n          if (target && target !== swiper.el) return;\n          newWidth = contentRect ? contentRect.width : (contentBoxSize[0] || contentBoxSize).inlineSize;\n          newHeight = contentRect ? contentRect.height : (contentBoxSize[0] || contentBoxSize).blockSize;\n        });\n        if (newWidth !== width || newHeight !== height) {\n          resizeHandler();\n        }\n      });\n    });\n    observer.observe(swiper.el);\n  };\n  const removeObserver = () => {\n    if (animationFrame) {\n      window.cancelAnimationFrame(animationFrame);\n    }\n    if (observer && observer.unobserve && swiper.el) {\n      observer.unobserve(swiper.el);\n      observer = null;\n    }\n  };\n  const orientationChangeHandler = () => {\n    if (!swiper || swiper.destroyed || !swiper.initialized) return;\n    emit('orientationchange');\n  };\n  on('init', () => {\n    if (swiper.params.resizeObserver && typeof window.ResizeObserver !== 'undefined') {\n      createObserver();\n      return;\n    }\n    window.addEventListener('resize', resizeHandler);\n    window.addEventListener('orientationchange', orientationChangeHandler);\n  });\n  on('destroy', () => {\n    removeObserver();\n    window.removeEventListener('resize', resizeHandler);\n    window.removeEventListener('orientationchange', orientationChangeHandler);\n  });\n}\nfunction Observer(_ref) {\n  let {\n    swiper,\n    extendParams,\n    on,\n    emit\n  } = _ref;\n  const observers = [];\n  const window = getWindow();\n  const attach = function (target, options) {\n    if (options === void 0) {\n      options = {};\n    }\n    const ObserverFunc = window.MutationObserver || window.WebkitMutationObserver;\n    const observer = new ObserverFunc(mutations => {\n      // The observerUpdate event should only be triggered\n      // once despite the number of mutations.  Additional\n      // triggers are redundant and are very costly\n      if (swiper.__preventObserver__) return;\n      if (mutations.length === 1) {\n        emit('observerUpdate', mutations[0]);\n        return;\n      }\n      const observerUpdate = function observerUpdate() {\n        emit('observerUpdate', mutations[0]);\n      };\n      if (window.requestAnimationFrame) {\n        window.requestAnimationFrame(observerUpdate);\n      } else {\n        window.setTimeout(observerUpdate, 0);\n      }\n    });\n    observer.observe(target, {\n      attributes: typeof options.attributes === 'undefined' ? true : options.attributes,\n      childList: swiper.isElement || (typeof options.childList === 'undefined' ? true : options).childList,\n      characterData: typeof options.characterData === 'undefined' ? true : options.characterData\n    });\n    observers.push(observer);\n  };\n  const init = () => {\n    if (!swiper.params.observer) return;\n    if (swiper.params.observeParents) {\n      const containerParents = elementParents(swiper.hostEl);\n      for (let i = 0; i < containerParents.length; i += 1) {\n        attach(containerParents[i]);\n      }\n    }\n    // Observe container\n    attach(swiper.hostEl, {\n      childList: swiper.params.observeSlideChildren\n    });\n\n    // Observe wrapper\n    attach(swiper.wrapperEl, {\n      attributes: false\n    });\n  };\n  const destroy = () => {\n    observers.forEach(observer => {\n      observer.disconnect();\n    });\n    observers.splice(0, observers.length);\n  };\n  extendParams({\n    observer: false,\n    observeParents: false,\n    observeSlideChildren: false\n  });\n  on('init', init);\n  on('destroy', destroy);\n}\n\n/* eslint-disable no-underscore-dangle */\n\nvar eventsEmitter = {\n  on(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    events.split(' ').forEach(event => {\n      if (!self.eventsListeners[event]) self.eventsListeners[event] = [];\n      self.eventsListeners[event][method](handler);\n    });\n    return self;\n  },\n  once(events, handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    function onceHandler() {\n      self.off(events, onceHandler);\n      if (onceHandler.__emitterProxy) {\n        delete onceHandler.__emitterProxy;\n      }\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      handler.apply(self, args);\n    }\n    onceHandler.__emitterProxy = handler;\n    return self.on(events, onceHandler, priority);\n  },\n  onAny(handler, priority) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (typeof handler !== 'function') return self;\n    const method = priority ? 'unshift' : 'push';\n    if (self.eventsAnyListeners.indexOf(handler) < 0) {\n      self.eventsAnyListeners[method](handler);\n    }\n    return self;\n  },\n  offAny(handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsAnyListeners) return self;\n    const index = self.eventsAnyListeners.indexOf(handler);\n    if (index >= 0) {\n      self.eventsAnyListeners.splice(index, 1);\n    }\n    return self;\n  },\n  off(events, handler) {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    events.split(' ').forEach(event => {\n      if (typeof handler === 'undefined') {\n        self.eventsListeners[event] = [];\n      } else if (self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach((eventHandler, index) => {\n          if (eventHandler === handler || eventHandler.__emitterProxy && eventHandler.__emitterProxy === handler) {\n            self.eventsListeners[event].splice(index, 1);\n          }\n        });\n      }\n    });\n    return self;\n  },\n  emit() {\n    const self = this;\n    if (!self.eventsListeners || self.destroyed) return self;\n    if (!self.eventsListeners) return self;\n    let events;\n    let data;\n    let context;\n    for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n      args[_key2] = arguments[_key2];\n    }\n    if (typeof args[0] === 'string' || Array.isArray(args[0])) {\n      events = args[0];\n      data = args.slice(1, args.length);\n      context = self;\n    } else {\n      events = args[0].events;\n      data = args[0].data;\n      context = args[0].context || self;\n    }\n    data.unshift(context);\n    const eventsArray = Array.isArray(events) ? events : events.split(' ');\n    eventsArray.forEach(event => {\n      if (self.eventsAnyListeners && self.eventsAnyListeners.length) {\n        self.eventsAnyListeners.forEach(eventHandler => {\n          eventHandler.apply(context, [event, ...data]);\n        });\n      }\n      if (self.eventsListeners && self.eventsListeners[event]) {\n        self.eventsListeners[event].forEach(eventHandler => {\n          eventHandler.apply(context, data);\n        });\n      }\n    });\n    return self;\n  }\n};\nfunction updateSize() {\n  const swiper = this;\n  let width;\n  let height;\n  const el = swiper.el;\n  if (typeof swiper.params.width !== 'undefined' && swiper.params.width !== null) {\n    width = swiper.params.width;\n  } else {\n    width = el.clientWidth;\n  }\n  if (typeof swiper.params.height !== 'undefined' && swiper.params.height !== null) {\n    height = swiper.params.height;\n  } else {\n    height = el.clientHeight;\n  }\n  if (width === 0 && swiper.isHorizontal() || height === 0 && swiper.isVertical()) {\n    return;\n  }\n\n  // Subtract paddings\n  width = width - parseInt(elementStyle(el, 'padding-left') || 0, 10) - parseInt(elementStyle(el, 'padding-right') || 0, 10);\n  height = height - parseInt(elementStyle(el, 'padding-top') || 0, 10) - parseInt(elementStyle(el, 'padding-bottom') || 0, 10);\n  if (Number.isNaN(width)) width = 0;\n  if (Number.isNaN(height)) height = 0;\n  Object.assign(swiper, {\n    width,\n    height,\n    size: swiper.isHorizontal() ? width : height\n  });\n}\nfunction updateSlides() {\n  const swiper = this;\n  function getDirectionPropertyValue(node, label) {\n    return parseFloat(node.getPropertyValue(swiper.getDirectionLabel(label)) || 0);\n  }\n  const params = swiper.params;\n  const {\n    wrapperEl,\n    slidesEl,\n    size: swiperSize,\n    rtlTranslate: rtl,\n    wrongRTL\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const previousSlidesLength = isVirtual ? swiper.virtual.slides.length : swiper.slides.length;\n  const slides = elementChildren(slidesEl, `.${swiper.params.slideClass}, swiper-slide`);\n  const slidesLength = isVirtual ? swiper.virtual.slides.length : slides.length;\n  let snapGrid = [];\n  const slidesGrid = [];\n  const slidesSizesGrid = [];\n  let offsetBefore = params.slidesOffsetBefore;\n  if (typeof offsetBefore === 'function') {\n    offsetBefore = params.slidesOffsetBefore.call(swiper);\n  }\n  let offsetAfter = params.slidesOffsetAfter;\n  if (typeof offsetAfter === 'function') {\n    offsetAfter = params.slidesOffsetAfter.call(swiper);\n  }\n  const previousSnapGridLength = swiper.snapGrid.length;\n  const previousSlidesGridLength = swiper.slidesGrid.length;\n  let spaceBetween = params.spaceBetween;\n  let slidePosition = -offsetBefore;\n  let prevSlideSize = 0;\n  let index = 0;\n  if (typeof swiperSize === 'undefined') {\n    return;\n  }\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiperSize;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  swiper.virtualSize = -spaceBetween;\n\n  // reset margins\n  slides.forEach(slideEl => {\n    if (rtl) {\n      slideEl.style.marginLeft = '';\n    } else {\n      slideEl.style.marginRight = '';\n    }\n    slideEl.style.marginBottom = '';\n    slideEl.style.marginTop = '';\n  });\n\n  // reset cssMode offsets\n  if (params.centeredSlides && params.cssMode) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', '');\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', '');\n  }\n  const gridEnabled = params.grid && params.grid.rows > 1 && swiper.grid;\n  if (gridEnabled) {\n    swiper.grid.initSlides(slides);\n  } else if (swiper.grid) {\n    swiper.grid.unsetSlides();\n  }\n\n  // Calc slides\n  let slideSize;\n  const shouldResetSlideSize = params.slidesPerView === 'auto' && params.breakpoints && Object.keys(params.breakpoints).filter(key => {\n    return typeof params.breakpoints[key].slidesPerView !== 'undefined';\n  }).length > 0;\n  for (let i = 0; i < slidesLength; i += 1) {\n    slideSize = 0;\n    let slide;\n    if (slides[i]) slide = slides[i];\n    if (gridEnabled) {\n      swiper.grid.updateSlide(i, slide, slides);\n    }\n    if (slides[i] && elementStyle(slide, 'display') === 'none') continue; // eslint-disable-line\n\n    if (params.slidesPerView === 'auto') {\n      if (shouldResetSlideSize) {\n        slides[i].style[swiper.getDirectionLabel('width')] = ``;\n      }\n      const slideStyles = getComputedStyle(slide);\n      const currentTransform = slide.style.transform;\n      const currentWebKitTransform = slide.style.webkitTransform;\n      if (currentTransform) {\n        slide.style.transform = 'none';\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = 'none';\n      }\n      if (params.roundLengths) {\n        slideSize = swiper.isHorizontal() ? elementOuterSize(slide, 'width', true) : elementOuterSize(slide, 'height', true);\n      } else {\n        // eslint-disable-next-line\n        const width = getDirectionPropertyValue(slideStyles, 'width');\n        const paddingLeft = getDirectionPropertyValue(slideStyles, 'padding-left');\n        const paddingRight = getDirectionPropertyValue(slideStyles, 'padding-right');\n        const marginLeft = getDirectionPropertyValue(slideStyles, 'margin-left');\n        const marginRight = getDirectionPropertyValue(slideStyles, 'margin-right');\n        const boxSizing = slideStyles.getPropertyValue('box-sizing');\n        if (boxSizing && boxSizing === 'border-box') {\n          slideSize = width + marginLeft + marginRight;\n        } else {\n          const {\n            clientWidth,\n            offsetWidth\n          } = slide;\n          slideSize = width + paddingLeft + paddingRight + marginLeft + marginRight + (offsetWidth - clientWidth);\n        }\n      }\n      if (currentTransform) {\n        slide.style.transform = currentTransform;\n      }\n      if (currentWebKitTransform) {\n        slide.style.webkitTransform = currentWebKitTransform;\n      }\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n    } else {\n      slideSize = (swiperSize - (params.slidesPerView - 1) * spaceBetween) / params.slidesPerView;\n      if (params.roundLengths) slideSize = Math.floor(slideSize);\n      if (slides[i]) {\n        slides[i].style[swiper.getDirectionLabel('width')] = `${slideSize}px`;\n      }\n    }\n    if (slides[i]) {\n      slides[i].swiperSlideSize = slideSize;\n    }\n    slidesSizesGrid.push(slideSize);\n    if (params.centeredSlides) {\n      slidePosition = slidePosition + slideSize / 2 + prevSlideSize / 2 + spaceBetween;\n      if (prevSlideSize === 0 && i !== 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (i === 0) slidePosition = slidePosition - swiperSize / 2 - spaceBetween;\n      if (Math.abs(slidePosition) < 1 / 1000) slidePosition = 0;\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if (index % params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n    } else {\n      if (params.roundLengths) slidePosition = Math.floor(slidePosition);\n      if ((index - Math.min(swiper.params.slidesPerGroupSkip, index)) % swiper.params.slidesPerGroup === 0) snapGrid.push(slidePosition);\n      slidesGrid.push(slidePosition);\n      slidePosition = slidePosition + slideSize + spaceBetween;\n    }\n    swiper.virtualSize += slideSize + spaceBetween;\n    prevSlideSize = slideSize;\n    index += 1;\n  }\n  swiper.virtualSize = Math.max(swiper.virtualSize, swiperSize) + offsetAfter;\n  if (rtl && wrongRTL && (params.effect === 'slide' || params.effect === 'coverflow')) {\n    wrapperEl.style.width = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (params.setWrapperSize) {\n    wrapperEl.style[swiper.getDirectionLabel('width')] = `${swiper.virtualSize + spaceBetween}px`;\n  }\n  if (gridEnabled) {\n    swiper.grid.updateWrapperSize(slideSize, snapGrid);\n  }\n\n  // Remove last grid elements depending on width\n  if (!params.centeredSlides) {\n    const newSlidesGrid = [];\n    for (let i = 0; i < snapGrid.length; i += 1) {\n      let slidesGridItem = snapGrid[i];\n      if (params.roundLengths) slidesGridItem = Math.floor(slidesGridItem);\n      if (snapGrid[i] <= swiper.virtualSize - swiperSize) {\n        newSlidesGrid.push(slidesGridItem);\n      }\n    }\n    snapGrid = newSlidesGrid;\n    if (Math.floor(swiper.virtualSize - swiperSize) - Math.floor(snapGrid[snapGrid.length - 1]) > 1) {\n      snapGrid.push(swiper.virtualSize - swiperSize);\n    }\n  }\n  if (isVirtual && params.loop) {\n    const size = slidesSizesGrid[0] + spaceBetween;\n    if (params.slidesPerGroup > 1) {\n      const groups = Math.ceil((swiper.virtual.slidesBefore + swiper.virtual.slidesAfter) / params.slidesPerGroup);\n      const groupSize = size * params.slidesPerGroup;\n      for (let i = 0; i < groups; i += 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + groupSize);\n      }\n    }\n    for (let i = 0; i < swiper.virtual.slidesBefore + swiper.virtual.slidesAfter; i += 1) {\n      if (params.slidesPerGroup === 1) {\n        snapGrid.push(snapGrid[snapGrid.length - 1] + size);\n      }\n      slidesGrid.push(slidesGrid[slidesGrid.length - 1] + size);\n      swiper.virtualSize += size;\n    }\n  }\n  if (snapGrid.length === 0) snapGrid = [0];\n  if (spaceBetween !== 0) {\n    const key = swiper.isHorizontal() && rtl ? 'marginLeft' : swiper.getDirectionLabel('marginRight');\n    slides.filter((_, slideIndex) => {\n      if (!params.cssMode || params.loop) return true;\n      if (slideIndex === slides.length - 1) {\n        return false;\n      }\n      return true;\n    }).forEach(slideEl => {\n      slideEl.style[key] = `${spaceBetween}px`;\n    });\n  }\n  if (params.centeredSlides && params.centeredSlidesBounds) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const maxSnap = allSlidesSize > swiperSize ? allSlidesSize - swiperSize : 0;\n    snapGrid = snapGrid.map(snap => {\n      if (snap <= 0) return -offsetBefore;\n      if (snap > maxSnap) return maxSnap + offsetAfter;\n      return snap;\n    });\n  }\n  if (params.centerInsufficientSlides) {\n    let allSlidesSize = 0;\n    slidesSizesGrid.forEach(slideSizeValue => {\n      allSlidesSize += slideSizeValue + (spaceBetween || 0);\n    });\n    allSlidesSize -= spaceBetween;\n    const offsetSize = (params.slidesOffsetBefore || 0) + (params.slidesOffsetAfter || 0);\n    if (allSlidesSize + offsetSize < swiperSize) {\n      const allSlidesOffset = (swiperSize - allSlidesSize - offsetSize) / 2;\n      snapGrid.forEach((snap, snapIndex) => {\n        snapGrid[snapIndex] = snap - allSlidesOffset;\n      });\n      slidesGrid.forEach((snap, snapIndex) => {\n        slidesGrid[snapIndex] = snap + allSlidesOffset;\n      });\n    }\n  }\n  Object.assign(swiper, {\n    slides,\n    snapGrid,\n    slidesGrid,\n    slidesSizesGrid\n  });\n  if (params.centeredSlides && params.cssMode && !params.centeredSlidesBounds) {\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-before', `${-snapGrid[0]}px`);\n    setCSSProperty(wrapperEl, '--swiper-centered-offset-after', `${swiper.size / 2 - slidesSizesGrid[slidesSizesGrid.length - 1] / 2}px`);\n    const addToSnapGrid = -swiper.snapGrid[0];\n    const addToSlidesGrid = -swiper.slidesGrid[0];\n    swiper.snapGrid = swiper.snapGrid.map(v => v + addToSnapGrid);\n    swiper.slidesGrid = swiper.slidesGrid.map(v => v + addToSlidesGrid);\n  }\n  if (slidesLength !== previousSlidesLength) {\n    swiper.emit('slidesLengthChange');\n  }\n  if (snapGrid.length !== previousSnapGridLength) {\n    if (swiper.params.watchOverflow) swiper.checkOverflow();\n    swiper.emit('snapGridLengthChange');\n  }\n  if (slidesGrid.length !== previousSlidesGridLength) {\n    swiper.emit('slidesGridLengthChange');\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  swiper.emit('slidesUpdated');\n  if (!isVirtual && !params.cssMode && (params.effect === 'slide' || params.effect === 'fade')) {\n    const backFaceHiddenClass = `${params.containerModifierClass}backface-hidden`;\n    const hasClassBackfaceClassAdded = swiper.el.classList.contains(backFaceHiddenClass);\n    if (slidesLength <= params.maxBackfaceHiddenSlides) {\n      if (!hasClassBackfaceClassAdded) swiper.el.classList.add(backFaceHiddenClass);\n    } else if (hasClassBackfaceClassAdded) {\n      swiper.el.classList.remove(backFaceHiddenClass);\n    }\n  }\n}\nfunction updateAutoHeight(speed) {\n  const swiper = this;\n  const activeSlides = [];\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  let newHeight = 0;\n  let i;\n  if (typeof speed === 'number') {\n    swiper.setTransition(speed);\n  } else if (speed === true) {\n    swiper.setTransition(swiper.params.speed);\n  }\n  const getSlideByIndex = index => {\n    if (isVirtual) {\n      return swiper.slides[swiper.getSlideIndexByData(index)];\n    }\n    return swiper.slides[index];\n  };\n  // Find slides currently in view\n  if (swiper.params.slidesPerView !== 'auto' && swiper.params.slidesPerView > 1) {\n    if (swiper.params.centeredSlides) {\n      (swiper.visibleSlides || []).forEach(slide => {\n        activeSlides.push(slide);\n      });\n    } else {\n      for (i = 0; i < Math.ceil(swiper.params.slidesPerView); i += 1) {\n        const index = swiper.activeIndex + i;\n        if (index > swiper.slides.length && !isVirtual) break;\n        activeSlides.push(getSlideByIndex(index));\n      }\n    }\n  } else {\n    activeSlides.push(getSlideByIndex(swiper.activeIndex));\n  }\n\n  // Find new height from highest slide in view\n  for (i = 0; i < activeSlides.length; i += 1) {\n    if (typeof activeSlides[i] !== 'undefined') {\n      const height = activeSlides[i].offsetHeight;\n      newHeight = height > newHeight ? height : newHeight;\n    }\n  }\n\n  // Update Height\n  if (newHeight || newHeight === 0) swiper.wrapperEl.style.height = `${newHeight}px`;\n}\nfunction updateSlidesOffset() {\n  const swiper = this;\n  const slides = swiper.slides;\n  // eslint-disable-next-line\n  const minusOffset = swiper.isElement ? swiper.isHorizontal() ? swiper.wrapperEl.offsetLeft : swiper.wrapperEl.offsetTop : 0;\n  for (let i = 0; i < slides.length; i += 1) {\n    slides[i].swiperSlideOffset = (swiper.isHorizontal() ? slides[i].offsetLeft : slides[i].offsetTop) - minusOffset - swiper.cssOverflowAdjustment();\n  }\n}\nconst toggleSlideClasses$1 = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesProgress(translate) {\n  if (translate === void 0) {\n    translate = this && this.translate || 0;\n  }\n  const swiper = this;\n  const params = swiper.params;\n  const {\n    slides,\n    rtlTranslate: rtl,\n    snapGrid\n  } = swiper;\n  if (slides.length === 0) return;\n  if (typeof slides[0].swiperSlideOffset === 'undefined') swiper.updateSlidesOffset();\n  let offsetCenter = -translate;\n  if (rtl) offsetCenter = translate;\n  swiper.visibleSlidesIndexes = [];\n  swiper.visibleSlides = [];\n  let spaceBetween = params.spaceBetween;\n  if (typeof spaceBetween === 'string' && spaceBetween.indexOf('%') >= 0) {\n    spaceBetween = parseFloat(spaceBetween.replace('%', '')) / 100 * swiper.size;\n  } else if (typeof spaceBetween === 'string') {\n    spaceBetween = parseFloat(spaceBetween);\n  }\n  for (let i = 0; i < slides.length; i += 1) {\n    const slide = slides[i];\n    let slideOffset = slide.swiperSlideOffset;\n    if (params.cssMode && params.centeredSlides) {\n      slideOffset -= slides[0].swiperSlideOffset;\n    }\n    const slideProgress = (offsetCenter + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const originalSlideProgress = (offsetCenter - snapGrid[0] + (params.centeredSlides ? swiper.minTranslate() : 0) - slideOffset) / (slide.swiperSlideSize + spaceBetween);\n    const slideBefore = -(offsetCenter - slideOffset);\n    const slideAfter = slideBefore + swiper.slidesSizesGrid[i];\n    const isFullyVisible = slideBefore >= 0 && slideBefore <= swiper.size - swiper.slidesSizesGrid[i];\n    const isVisible = slideBefore >= 0 && slideBefore < swiper.size - 1 || slideAfter > 1 && slideAfter <= swiper.size || slideBefore <= 0 && slideAfter >= swiper.size;\n    if (isVisible) {\n      swiper.visibleSlides.push(slide);\n      swiper.visibleSlidesIndexes.push(i);\n    }\n    toggleSlideClasses$1(slide, isVisible, params.slideVisibleClass);\n    toggleSlideClasses$1(slide, isFullyVisible, params.slideFullyVisibleClass);\n    slide.progress = rtl ? -slideProgress : slideProgress;\n    slide.originalProgress = rtl ? -originalSlideProgress : originalSlideProgress;\n  }\n}\nfunction updateProgress(translate) {\n  const swiper = this;\n  if (typeof translate === 'undefined') {\n    const multiplier = swiper.rtlTranslate ? -1 : 1;\n    // eslint-disable-next-line\n    translate = swiper && swiper.translate && swiper.translate * multiplier || 0;\n  }\n  const params = swiper.params;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  let {\n    progress,\n    isBeginning,\n    isEnd,\n    progressLoop\n  } = swiper;\n  const wasBeginning = isBeginning;\n  const wasEnd = isEnd;\n  if (translatesDiff === 0) {\n    progress = 0;\n    isBeginning = true;\n    isEnd = true;\n  } else {\n    progress = (translate - swiper.minTranslate()) / translatesDiff;\n    const isBeginningRounded = Math.abs(translate - swiper.minTranslate()) < 1;\n    const isEndRounded = Math.abs(translate - swiper.maxTranslate()) < 1;\n    isBeginning = isBeginningRounded || progress <= 0;\n    isEnd = isEndRounded || progress >= 1;\n    if (isBeginningRounded) progress = 0;\n    if (isEndRounded) progress = 1;\n  }\n  if (params.loop) {\n    const firstSlideIndex = swiper.getSlideIndexByData(0);\n    const lastSlideIndex = swiper.getSlideIndexByData(swiper.slides.length - 1);\n    const firstSlideTranslate = swiper.slidesGrid[firstSlideIndex];\n    const lastSlideTranslate = swiper.slidesGrid[lastSlideIndex];\n    const translateMax = swiper.slidesGrid[swiper.slidesGrid.length - 1];\n    const translateAbs = Math.abs(translate);\n    if (translateAbs >= firstSlideTranslate) {\n      progressLoop = (translateAbs - firstSlideTranslate) / translateMax;\n    } else {\n      progressLoop = (translateAbs + translateMax - lastSlideTranslate) / translateMax;\n    }\n    if (progressLoop > 1) progressLoop -= 1;\n  }\n  Object.assign(swiper, {\n    progress,\n    progressLoop,\n    isBeginning,\n    isEnd\n  });\n  if (params.watchSlidesProgress || params.centeredSlides && params.autoHeight) swiper.updateSlidesProgress(translate);\n  if (isBeginning && !wasBeginning) {\n    swiper.emit('reachBeginning toEdge');\n  }\n  if (isEnd && !wasEnd) {\n    swiper.emit('reachEnd toEdge');\n  }\n  if (wasBeginning && !isBeginning || wasEnd && !isEnd) {\n    swiper.emit('fromEdge');\n  }\n  swiper.emit('progress', progress);\n}\nconst toggleSlideClasses = (slideEl, condition, className) => {\n  if (condition && !slideEl.classList.contains(className)) {\n    slideEl.classList.add(className);\n  } else if (!condition && slideEl.classList.contains(className)) {\n    slideEl.classList.remove(className);\n  }\n};\nfunction updateSlidesClasses() {\n  const swiper = this;\n  const {\n    slides,\n    params,\n    slidesEl,\n    activeIndex\n  } = swiper;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  const getFilteredSlide = selector => {\n    return elementChildren(slidesEl, `.${params.slideClass}${selector}, swiper-slide${selector}`)[0];\n  };\n  let activeSlide;\n  let prevSlide;\n  let nextSlide;\n  if (isVirtual) {\n    if (params.loop) {\n      let slideIndex = activeIndex - swiper.virtual.slidesBefore;\n      if (slideIndex < 0) slideIndex = swiper.virtual.slides.length + slideIndex;\n      if (slideIndex >= swiper.virtual.slides.length) slideIndex -= swiper.virtual.slides.length;\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${slideIndex}\"]`);\n    } else {\n      activeSlide = getFilteredSlide(`[data-swiper-slide-index=\"${activeIndex}\"]`);\n    }\n  } else {\n    if (gridEnabled) {\n      activeSlide = slides.find(slideEl => slideEl.column === activeIndex);\n      nextSlide = slides.find(slideEl => slideEl.column === activeIndex + 1);\n      prevSlide = slides.find(slideEl => slideEl.column === activeIndex - 1);\n    } else {\n      activeSlide = slides[activeIndex];\n    }\n  }\n  if (activeSlide) {\n    if (!gridEnabled) {\n      // Next Slide\n      nextSlide = elementNextAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !nextSlide) {\n        nextSlide = slides[0];\n      }\n\n      // Prev Slide\n      prevSlide = elementPrevAll(activeSlide, `.${params.slideClass}, swiper-slide`)[0];\n      if (params.loop && !prevSlide === 0) {\n        prevSlide = slides[slides.length - 1];\n      }\n    }\n  }\n  slides.forEach(slideEl => {\n    toggleSlideClasses(slideEl, slideEl === activeSlide, params.slideActiveClass);\n    toggleSlideClasses(slideEl, slideEl === nextSlide, params.slideNextClass);\n    toggleSlideClasses(slideEl, slideEl === prevSlide, params.slidePrevClass);\n  });\n  swiper.emitSlidesClasses();\n}\nconst processLazyPreloader = (swiper, imageEl) => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  const slideSelector = () => swiper.isElement ? `swiper-slide` : `.${swiper.params.slideClass}`;\n  const slideEl = imageEl.closest(slideSelector());\n  if (slideEl) {\n    let lazyEl = slideEl.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n    if (!lazyEl && swiper.isElement) {\n      if (slideEl.shadowRoot) {\n        lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n      } else {\n        // init later\n        requestAnimationFrame(() => {\n          if (slideEl.shadowRoot) {\n            lazyEl = slideEl.shadowRoot.querySelector(`.${swiper.params.lazyPreloaderClass}`);\n            if (lazyEl) lazyEl.remove();\n          }\n        });\n      }\n    }\n    if (lazyEl) lazyEl.remove();\n  }\n};\nconst unlazy = (swiper, index) => {\n  if (!swiper.slides[index]) return;\n  const imageEl = swiper.slides[index].querySelector('[loading=\"lazy\"]');\n  if (imageEl) imageEl.removeAttribute('loading');\n};\nconst preload = swiper => {\n  if (!swiper || swiper.destroyed || !swiper.params) return;\n  let amount = swiper.params.lazyPreloadPrevNext;\n  const len = swiper.slides.length;\n  if (!len || !amount || amount < 0) return;\n  amount = Math.min(amount, len);\n  const slidesPerView = swiper.params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(swiper.params.slidesPerView);\n  const activeIndex = swiper.activeIndex;\n  if (swiper.params.grid && swiper.params.grid.rows > 1) {\n    const activeColumn = activeIndex;\n    const preloadColumns = [activeColumn - amount];\n    preloadColumns.push(...Array.from({\n      length: amount\n    }).map((_, i) => {\n      return activeColumn + slidesPerView + i;\n    }));\n    swiper.slides.forEach((slideEl, i) => {\n      if (preloadColumns.includes(slideEl.column)) unlazy(swiper, i);\n    });\n    return;\n  }\n  const slideIndexLastInView = activeIndex + slidesPerView - 1;\n  if (swiper.params.rewind || swiper.params.loop) {\n    for (let i = activeIndex - amount; i <= slideIndexLastInView + amount; i += 1) {\n      const realIndex = (i % len + len) % len;\n      if (realIndex < activeIndex || realIndex > slideIndexLastInView) unlazy(swiper, realIndex);\n    }\n  } else {\n    for (let i = Math.max(activeIndex - amount, 0); i <= Math.min(slideIndexLastInView + amount, len - 1); i += 1) {\n      if (i !== activeIndex && (i > slideIndexLastInView || i < activeIndex)) {\n        unlazy(swiper, i);\n      }\n    }\n  }\n};\nfunction getActiveIndexByTranslate(swiper) {\n  const {\n    slidesGrid,\n    params\n  } = swiper;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  let activeIndex;\n  for (let i = 0; i < slidesGrid.length; i += 1) {\n    if (typeof slidesGrid[i + 1] !== 'undefined') {\n      if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1] - (slidesGrid[i + 1] - slidesGrid[i]) / 2) {\n        activeIndex = i;\n      } else if (translate >= slidesGrid[i] && translate < slidesGrid[i + 1]) {\n        activeIndex = i + 1;\n      }\n    } else if (translate >= slidesGrid[i]) {\n      activeIndex = i;\n    }\n  }\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    if (activeIndex < 0 || typeof activeIndex === 'undefined') activeIndex = 0;\n  }\n  return activeIndex;\n}\nfunction updateActiveIndex(newActiveIndex) {\n  const swiper = this;\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  const {\n    snapGrid,\n    params,\n    activeIndex: previousIndex,\n    realIndex: previousRealIndex,\n    snapIndex: previousSnapIndex\n  } = swiper;\n  let activeIndex = newActiveIndex;\n  let snapIndex;\n  const getVirtualRealIndex = aIndex => {\n    let realIndex = aIndex - swiper.virtual.slidesBefore;\n    if (realIndex < 0) {\n      realIndex = swiper.virtual.slides.length + realIndex;\n    }\n    if (realIndex >= swiper.virtual.slides.length) {\n      realIndex -= swiper.virtual.slides.length;\n    }\n    return realIndex;\n  };\n  if (typeof activeIndex === 'undefined') {\n    activeIndex = getActiveIndexByTranslate(swiper);\n  }\n  if (snapGrid.indexOf(translate) >= 0) {\n    snapIndex = snapGrid.indexOf(translate);\n  } else {\n    const skip = Math.min(params.slidesPerGroupSkip, activeIndex);\n    snapIndex = skip + Math.floor((activeIndex - skip) / params.slidesPerGroup);\n  }\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  if (activeIndex === previousIndex && !swiper.params.loop) {\n    if (snapIndex !== previousSnapIndex) {\n      swiper.snapIndex = snapIndex;\n      swiper.emit('snapIndexChange');\n    }\n    return;\n  }\n  if (activeIndex === previousIndex && swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n    swiper.realIndex = getVirtualRealIndex(activeIndex);\n    return;\n  }\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n\n  // Get real index\n  let realIndex;\n  if (swiper.virtual && params.virtual.enabled && params.loop) {\n    realIndex = getVirtualRealIndex(activeIndex);\n  } else if (gridEnabled) {\n    const firstSlideInColumn = swiper.slides.find(slideEl => slideEl.column === activeIndex);\n    let activeSlideIndex = parseInt(firstSlideInColumn.getAttribute('data-swiper-slide-index'), 10);\n    if (Number.isNaN(activeSlideIndex)) {\n      activeSlideIndex = Math.max(swiper.slides.indexOf(firstSlideInColumn), 0);\n    }\n    realIndex = Math.floor(activeSlideIndex / params.grid.rows);\n  } else if (swiper.slides[activeIndex]) {\n    const slideIndex = swiper.slides[activeIndex].getAttribute('data-swiper-slide-index');\n    if (slideIndex) {\n      realIndex = parseInt(slideIndex, 10);\n    } else {\n      realIndex = activeIndex;\n    }\n  } else {\n    realIndex = activeIndex;\n  }\n  Object.assign(swiper, {\n    previousSnapIndex,\n    snapIndex,\n    previousRealIndex,\n    realIndex,\n    previousIndex,\n    activeIndex\n  });\n  if (swiper.initialized) {\n    preload(swiper);\n  }\n  swiper.emit('activeIndexChange');\n  swiper.emit('snapIndexChange');\n  if (swiper.initialized || swiper.params.runCallbacksOnInit) {\n    if (previousRealIndex !== realIndex) {\n      swiper.emit('realIndexChange');\n    }\n    swiper.emit('slideChange');\n  }\n}\nfunction updateClickedSlide(el, path) {\n  const swiper = this;\n  const params = swiper.params;\n  let slide = el.closest(`.${params.slideClass}, swiper-slide`);\n  if (!slide && swiper.isElement && path && path.length > 1 && path.includes(el)) {\n    [...path.slice(path.indexOf(el) + 1, path.length)].forEach(pathEl => {\n      if (!slide && pathEl.matches && pathEl.matches(`.${params.slideClass}, swiper-slide`)) {\n        slide = pathEl;\n      }\n    });\n  }\n  let slideFound = false;\n  let slideIndex;\n  if (slide) {\n    for (let i = 0; i < swiper.slides.length; i += 1) {\n      if (swiper.slides[i] === slide) {\n        slideFound = true;\n        slideIndex = i;\n        break;\n      }\n    }\n  }\n  if (slide && slideFound) {\n    swiper.clickedSlide = slide;\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.clickedIndex = parseInt(slide.getAttribute('data-swiper-slide-index'), 10);\n    } else {\n      swiper.clickedIndex = slideIndex;\n    }\n  } else {\n    swiper.clickedSlide = undefined;\n    swiper.clickedIndex = undefined;\n    return;\n  }\n  if (params.slideToClickedSlide && swiper.clickedIndex !== undefined && swiper.clickedIndex !== swiper.activeIndex) {\n    swiper.slideToClickedSlide();\n  }\n}\nvar update = {\n  updateSize,\n  updateSlides,\n  updateAutoHeight,\n  updateSlidesOffset,\n  updateSlidesProgress,\n  updateProgress,\n  updateSlidesClasses,\n  updateActiveIndex,\n  updateClickedSlide\n};\nfunction getSwiperTranslate(axis) {\n  if (axis === void 0) {\n    axis = this.isHorizontal() ? 'x' : 'y';\n  }\n  const swiper = this;\n  const {\n    params,\n    rtlTranslate: rtl,\n    translate,\n    wrapperEl\n  } = swiper;\n  if (params.virtualTranslate) {\n    return rtl ? -translate : translate;\n  }\n  if (params.cssMode) {\n    return translate;\n  }\n  let currentTranslate = getTranslate(wrapperEl, axis);\n  currentTranslate += swiper.cssOverflowAdjustment();\n  if (rtl) currentTranslate = -currentTranslate;\n  return currentTranslate || 0;\n}\nfunction setTranslate(translate, byController) {\n  const swiper = this;\n  const {\n    rtlTranslate: rtl,\n    params,\n    wrapperEl,\n    progress\n  } = swiper;\n  let x = 0;\n  let y = 0;\n  const z = 0;\n  if (swiper.isHorizontal()) {\n    x = rtl ? -translate : translate;\n  } else {\n    y = translate;\n  }\n  if (params.roundLengths) {\n    x = Math.floor(x);\n    y = Math.floor(y);\n  }\n  swiper.previousTranslate = swiper.translate;\n  swiper.translate = swiper.isHorizontal() ? x : y;\n  if (params.cssMode) {\n    wrapperEl[swiper.isHorizontal() ? 'scrollLeft' : 'scrollTop'] = swiper.isHorizontal() ? -x : -y;\n  } else if (!params.virtualTranslate) {\n    if (swiper.isHorizontal()) {\n      x -= swiper.cssOverflowAdjustment();\n    } else {\n      y -= swiper.cssOverflowAdjustment();\n    }\n    wrapperEl.style.transform = `translate3d(${x}px, ${y}px, ${z}px)`;\n  }\n\n  // Check if we need to update progress\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== progress) {\n    swiper.updateProgress(translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, byController);\n}\nfunction minTranslate() {\n  return -this.snapGrid[0];\n}\nfunction maxTranslate() {\n  return -this.snapGrid[this.snapGrid.length - 1];\n}\nfunction translateTo(translate, speed, runCallbacks, translateBounds, internal) {\n  if (translate === void 0) {\n    translate = 0;\n  }\n  if (speed === void 0) {\n    speed = this.params.speed;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (translateBounds === void 0) {\n    translateBounds = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    wrapperEl\n  } = swiper;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  const minTranslate = swiper.minTranslate();\n  const maxTranslate = swiper.maxTranslate();\n  let newTranslate;\n  if (translateBounds && translate > minTranslate) newTranslate = minTranslate;else if (translateBounds && translate < maxTranslate) newTranslate = maxTranslate;else newTranslate = translate;\n\n  // Update progress\n  swiper.updateProgress(newTranslate);\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    if (speed === 0) {\n      wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = -newTranslate;\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: -newTranslate,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: -newTranslate,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  if (speed === 0) {\n    swiper.setTransition(0);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionEnd');\n    }\n  } else {\n    swiper.setTransition(speed);\n    swiper.setTranslate(newTranslate);\n    if (runCallbacks) {\n      swiper.emit('beforeTransitionStart', speed, internal);\n      swiper.emit('transitionStart');\n    }\n    if (!swiper.animating) {\n      swiper.animating = true;\n      if (!swiper.onTranslateToWrapperTransitionEnd) {\n        swiper.onTranslateToWrapperTransitionEnd = function transitionEnd(e) {\n          if (!swiper || swiper.destroyed) return;\n          if (e.target !== this) return;\n          swiper.wrapperEl.removeEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n          swiper.onTranslateToWrapperTransitionEnd = null;\n          delete swiper.onTranslateToWrapperTransitionEnd;\n          swiper.animating = false;\n          if (runCallbacks) {\n            swiper.emit('transitionEnd');\n          }\n        };\n      }\n      swiper.wrapperEl.addEventListener('transitionend', swiper.onTranslateToWrapperTransitionEnd);\n    }\n  }\n  return true;\n}\nvar translate = {\n  getTranslate: getSwiperTranslate,\n  setTranslate,\n  minTranslate,\n  maxTranslate,\n  translateTo\n};\nfunction setTransition(duration, byController) {\n  const swiper = this;\n  if (!swiper.params.cssMode) {\n    swiper.wrapperEl.style.transitionDuration = `${duration}ms`;\n    swiper.wrapperEl.style.transitionDelay = duration === 0 ? `0ms` : '';\n  }\n  swiper.emit('setTransition', duration, byController);\n}\nfunction transitionEmit(_ref) {\n  let {\n    swiper,\n    runCallbacks,\n    direction,\n    step\n  } = _ref;\n  const {\n    activeIndex,\n    previousIndex\n  } = swiper;\n  let dir = direction;\n  if (!dir) {\n    if (activeIndex > previousIndex) dir = 'next';else if (activeIndex < previousIndex) dir = 'prev';else dir = 'reset';\n  }\n  swiper.emit(`transition${step}`);\n  if (runCallbacks && dir === 'reset') {\n    swiper.emit(`slideResetTransition${step}`);\n  } else if (runCallbacks && activeIndex !== previousIndex) {\n    swiper.emit(`slideChangeTransition${step}`);\n    if (dir === 'next') {\n      swiper.emit(`slideNextTransition${step}`);\n    } else {\n      swiper.emit(`slidePrevTransition${step}`);\n    }\n  }\n}\nfunction transitionStart(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  if (params.cssMode) return;\n  if (params.autoHeight) {\n    swiper.updateAutoHeight();\n  }\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'Start'\n  });\n}\nfunction transitionEnd(runCallbacks, direction) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.animating = false;\n  if (params.cssMode) return;\n  swiper.setTransition(0);\n  transitionEmit({\n    swiper,\n    runCallbacks,\n    direction,\n    step: 'End'\n  });\n}\nvar transition = {\n  setTransition,\n  transitionStart,\n  transitionEnd\n};\nfunction slideTo(index, speed, runCallbacks, internal, initial) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    index = parseInt(index, 10);\n  }\n  const swiper = this;\n  let slideIndex = index;\n  if (slideIndex < 0) slideIndex = 0;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    previousIndex,\n    activeIndex,\n    rtlTranslate: rtl,\n    wrapperEl,\n    enabled\n  } = swiper;\n  if (!enabled && !internal && !initial || swiper.destroyed || swiper.animating && params.preventInteractionOnTransition) {\n    return false;\n  }\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, slideIndex);\n  let snapIndex = skip + Math.floor((slideIndex - skip) / swiper.params.slidesPerGroup);\n  if (snapIndex >= snapGrid.length) snapIndex = snapGrid.length - 1;\n  const translate = -snapGrid[snapIndex];\n  // Normalize slideIndex\n  if (params.normalizeSlideIndex) {\n    for (let i = 0; i < slidesGrid.length; i += 1) {\n      const normalizedTranslate = -Math.floor(translate * 100);\n      const normalizedGrid = Math.floor(slidesGrid[i] * 100);\n      const normalizedGridNext = Math.floor(slidesGrid[i + 1] * 100);\n      if (typeof slidesGrid[i + 1] !== 'undefined') {\n        if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext - (normalizedGridNext - normalizedGrid) / 2) {\n          slideIndex = i;\n        } else if (normalizedTranslate >= normalizedGrid && normalizedTranslate < normalizedGridNext) {\n          slideIndex = i + 1;\n        }\n      } else if (normalizedTranslate >= normalizedGrid) {\n        slideIndex = i;\n      }\n    }\n  }\n  // Directions locks\n  if (swiper.initialized && slideIndex !== activeIndex) {\n    if (!swiper.allowSlideNext && (rtl ? translate > swiper.translate && translate > swiper.minTranslate() : translate < swiper.translate && translate < swiper.minTranslate())) {\n      return false;\n    }\n    if (!swiper.allowSlidePrev && translate > swiper.translate && translate > swiper.maxTranslate()) {\n      if ((activeIndex || 0) !== slideIndex) {\n        return false;\n      }\n    }\n  }\n  if (slideIndex !== (previousIndex || 0) && runCallbacks) {\n    swiper.emit('beforeSlideChangeStart');\n  }\n\n  // Update progress\n  swiper.updateProgress(translate);\n  let direction;\n  if (slideIndex > activeIndex) direction = 'next';else if (slideIndex < activeIndex) direction = 'prev';else direction = 'reset';\n\n  // initial virtual\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n  const isInitialVirtual = isVirtual && initial;\n  // Update Index\n  if (!isInitialVirtual && (rtl && -translate === swiper.translate || !rtl && translate === swiper.translate)) {\n    swiper.updateActiveIndex(slideIndex);\n    // Update Height\n    if (params.autoHeight) {\n      swiper.updateAutoHeight();\n    }\n    swiper.updateSlidesClasses();\n    if (params.effect !== 'slide') {\n      swiper.setTranslate(translate);\n    }\n    if (direction !== 'reset') {\n      swiper.transitionStart(runCallbacks, direction);\n      swiper.transitionEnd(runCallbacks, direction);\n    }\n    return false;\n  }\n  if (params.cssMode) {\n    const isH = swiper.isHorizontal();\n    const t = rtl ? translate : -translate;\n    if (speed === 0) {\n      if (isVirtual) {\n        swiper.wrapperEl.style.scrollSnapType = 'none';\n        swiper._immediateVirtual = true;\n      }\n      if (isVirtual && !swiper._cssModeVirtualInitialSet && swiper.params.initialSlide > 0) {\n        swiper._cssModeVirtualInitialSet = true;\n        requestAnimationFrame(() => {\n          wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n        });\n      } else {\n        wrapperEl[isH ? 'scrollLeft' : 'scrollTop'] = t;\n      }\n      if (isVirtual) {\n        requestAnimationFrame(() => {\n          swiper.wrapperEl.style.scrollSnapType = '';\n          swiper._immediateVirtual = false;\n        });\n      }\n    } else {\n      if (!swiper.support.smoothScroll) {\n        animateCSSModeScroll({\n          swiper,\n          targetPosition: t,\n          side: isH ? 'left' : 'top'\n        });\n        return true;\n      }\n      wrapperEl.scrollTo({\n        [isH ? 'left' : 'top']: t,\n        behavior: 'smooth'\n      });\n    }\n    return true;\n  }\n  const browser = getBrowser();\n  const isSafari = browser.isSafari;\n  if (isVirtual && !initial && isSafari && swiper.isElement) {\n    swiper.virtual.update(false, false, slideIndex);\n  }\n  swiper.setTransition(speed);\n  swiper.setTranslate(translate);\n  swiper.updateActiveIndex(slideIndex);\n  swiper.updateSlidesClasses();\n  swiper.emit('beforeTransitionStart', speed, internal);\n  swiper.transitionStart(runCallbacks, direction);\n  if (speed === 0) {\n    swiper.transitionEnd(runCallbacks, direction);\n  } else if (!swiper.animating) {\n    swiper.animating = true;\n    if (!swiper.onSlideToWrapperTransitionEnd) {\n      swiper.onSlideToWrapperTransitionEnd = function transitionEnd(e) {\n        if (!swiper || swiper.destroyed) return;\n        if (e.target !== this) return;\n        swiper.wrapperEl.removeEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n        swiper.onSlideToWrapperTransitionEnd = null;\n        delete swiper.onSlideToWrapperTransitionEnd;\n        swiper.transitionEnd(runCallbacks, direction);\n      };\n    }\n    swiper.wrapperEl.addEventListener('transitionend', swiper.onSlideToWrapperTransitionEnd);\n  }\n  return true;\n}\nfunction slideToLoop(index, speed, runCallbacks, internal) {\n  if (index === void 0) {\n    index = 0;\n  }\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (typeof index === 'string') {\n    const indexAsNumber = parseInt(index, 10);\n    index = indexAsNumber;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const gridEnabled = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  let newIndex = index;\n  if (swiper.params.loop) {\n    if (swiper.virtual && swiper.params.virtual.enabled) {\n      // eslint-disable-next-line\n      newIndex = newIndex + swiper.virtual.slidesBefore;\n    } else {\n      let targetSlideIndex;\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        targetSlideIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        targetSlideIndex = swiper.getSlideIndexByData(newIndex);\n      }\n      const cols = gridEnabled ? Math.ceil(swiper.slides.length / swiper.params.grid.rows) : swiper.slides.length;\n      const {\n        centeredSlides\n      } = swiper.params;\n      let slidesPerView = swiper.params.slidesPerView;\n      if (slidesPerView === 'auto') {\n        slidesPerView = swiper.slidesPerViewDynamic();\n      } else {\n        slidesPerView = Math.ceil(parseFloat(swiper.params.slidesPerView, 10));\n        if (centeredSlides && slidesPerView % 2 === 0) {\n          slidesPerView = slidesPerView + 1;\n        }\n      }\n      let needLoopFix = cols - targetSlideIndex < slidesPerView;\n      if (centeredSlides) {\n        needLoopFix = needLoopFix || targetSlideIndex < Math.ceil(slidesPerView / 2);\n      }\n      if (internal && centeredSlides && swiper.params.slidesPerView !== 'auto' && !gridEnabled) {\n        needLoopFix = false;\n      }\n      if (needLoopFix) {\n        const direction = centeredSlides ? targetSlideIndex < swiper.activeIndex ? 'prev' : 'next' : targetSlideIndex - swiper.activeIndex - 1 < swiper.params.slidesPerView ? 'next' : 'prev';\n        swiper.loopFix({\n          direction,\n          slideTo: true,\n          activeSlideIndex: direction === 'next' ? targetSlideIndex + 1 : targetSlideIndex - cols + 1,\n          slideRealIndex: direction === 'next' ? swiper.realIndex : undefined\n        });\n      }\n      if (gridEnabled) {\n        const slideIndex = newIndex * swiper.params.grid.rows;\n        newIndex = swiper.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === slideIndex).column;\n      } else {\n        newIndex = swiper.getSlideIndexByData(newIndex);\n      }\n    }\n  }\n  requestAnimationFrame(() => {\n    swiper.slideTo(newIndex, speed, runCallbacks, internal);\n  });\n  return swiper;\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideNext(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    enabled,\n    params,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let perGroup = params.slidesPerGroup;\n  if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n    perGroup = Math.max(swiper.slidesPerViewDynamic('current', true), 1);\n  }\n  const increment = swiper.activeIndex < params.slidesPerGroupSkip ? 1 : perGroup;\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'next'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n    if (swiper.activeIndex === swiper.slides.length - 1 && params.cssMode) {\n      requestAnimationFrame(() => {\n        swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n      });\n      return true;\n    }\n  }\n  if (params.rewind && swiper.isEnd) {\n    return swiper.slideTo(0, speed, runCallbacks, internal);\n  }\n  return swiper.slideTo(swiper.activeIndex + increment, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slidePrev(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  const {\n    params,\n    snapGrid,\n    slidesGrid,\n    rtlTranslate,\n    enabled,\n    animating\n  } = swiper;\n  if (!enabled || swiper.destroyed) return swiper;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  const isVirtual = swiper.virtual && params.virtual.enabled;\n  if (params.loop) {\n    if (animating && !isVirtual && params.loopPreventsSliding) return false;\n    swiper.loopFix({\n      direction: 'prev'\n    });\n    // eslint-disable-next-line\n    swiper._clientLeft = swiper.wrapperEl.clientLeft;\n  }\n  const translate = rtlTranslate ? swiper.translate : -swiper.translate;\n  function normalize(val) {\n    if (val < 0) return -Math.floor(Math.abs(val));\n    return Math.floor(val);\n  }\n  const normalizedTranslate = normalize(translate);\n  const normalizedSnapGrid = snapGrid.map(val => normalize(val));\n  const isFreeMode = params.freeMode && params.freeMode.enabled;\n  let prevSnap = snapGrid[normalizedSnapGrid.indexOf(normalizedTranslate) - 1];\n  if (typeof prevSnap === 'undefined' && (params.cssMode || isFreeMode)) {\n    let prevSnapIndex;\n    snapGrid.forEach((snap, snapIndex) => {\n      if (normalizedTranslate >= snap) {\n        // prevSnap = snap;\n        prevSnapIndex = snapIndex;\n      }\n    });\n    if (typeof prevSnapIndex !== 'undefined') {\n      prevSnap = isFreeMode ? snapGrid[prevSnapIndex] : snapGrid[prevSnapIndex > 0 ? prevSnapIndex - 1 : prevSnapIndex];\n    }\n  }\n  let prevIndex = 0;\n  if (typeof prevSnap !== 'undefined') {\n    prevIndex = slidesGrid.indexOf(prevSnap);\n    if (prevIndex < 0) prevIndex = swiper.activeIndex - 1;\n    if (params.slidesPerView === 'auto' && params.slidesPerGroup === 1 && params.slidesPerGroupAuto) {\n      prevIndex = prevIndex - swiper.slidesPerViewDynamic('previous', true) + 1;\n      prevIndex = Math.max(prevIndex, 0);\n    }\n  }\n  if (params.rewind && swiper.isBeginning) {\n    const lastIndex = swiper.params.virtual && swiper.params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    return swiper.slideTo(lastIndex, speed, runCallbacks, internal);\n  } else if (params.loop && swiper.activeIndex === 0 && params.cssMode) {\n    requestAnimationFrame(() => {\n      swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n    });\n    return true;\n  }\n  return swiper.slideTo(prevIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideReset(speed, runCallbacks, internal) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  return swiper.slideTo(swiper.activeIndex, speed, runCallbacks, internal);\n}\n\n/* eslint no-unused-vars: \"off\" */\nfunction slideToClosest(speed, runCallbacks, internal, threshold) {\n  if (runCallbacks === void 0) {\n    runCallbacks = true;\n  }\n  if (threshold === void 0) {\n    threshold = 0.5;\n  }\n  const swiper = this;\n  if (swiper.destroyed) return;\n  if (typeof speed === 'undefined') {\n    speed = swiper.params.speed;\n  }\n  let index = swiper.activeIndex;\n  const skip = Math.min(swiper.params.slidesPerGroupSkip, index);\n  const snapIndex = skip + Math.floor((index - skip) / swiper.params.slidesPerGroup);\n  const translate = swiper.rtlTranslate ? swiper.translate : -swiper.translate;\n  if (translate >= swiper.snapGrid[snapIndex]) {\n    // The current translate is on or after the current snap index, so the choice\n    // is between the current index and the one after it.\n    const currentSnap = swiper.snapGrid[snapIndex];\n    const nextSnap = swiper.snapGrid[snapIndex + 1];\n    if (translate - currentSnap > (nextSnap - currentSnap) * threshold) {\n      index += swiper.params.slidesPerGroup;\n    }\n  } else {\n    // The current translate is before the current snap index, so the choice\n    // is between the current index and the one before it.\n    const prevSnap = swiper.snapGrid[snapIndex - 1];\n    const currentSnap = swiper.snapGrid[snapIndex];\n    if (translate - prevSnap <= (currentSnap - prevSnap) * threshold) {\n      index -= swiper.params.slidesPerGroup;\n    }\n  }\n  index = Math.max(index, 0);\n  index = Math.min(index, swiper.slidesGrid.length - 1);\n  return swiper.slideTo(index, speed, runCallbacks, internal);\n}\nfunction slideToClickedSlide() {\n  const swiper = this;\n  if (swiper.destroyed) return;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  const slidesPerView = params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : params.slidesPerView;\n  let slideToIndex = swiper.getSlideIndexWhenGrid(swiper.clickedIndex);\n  let realIndex;\n  const slideSelector = swiper.isElement ? `swiper-slide` : `.${params.slideClass}`;\n  const isGrid = swiper.grid && swiper.params.grid && swiper.params.grid.rows > 1;\n  if (params.loop) {\n    if (swiper.animating) return;\n    realIndex = parseInt(swiper.clickedSlide.getAttribute('data-swiper-slide-index'), 10);\n    if (params.centeredSlides) {\n      swiper.slideToLoop(realIndex);\n    } else if (slideToIndex > (isGrid ? (swiper.slides.length - slidesPerView) / 2 - (swiper.params.grid.rows - 1) : swiper.slides.length - slidesPerView)) {\n      swiper.loopFix();\n      slideToIndex = swiper.getSlideIndex(elementChildren(slidesEl, `${slideSelector}[data-swiper-slide-index=\"${realIndex}\"]`)[0]);\n      nextTick(() => {\n        swiper.slideTo(slideToIndex);\n      });\n    } else {\n      swiper.slideTo(slideToIndex);\n    }\n  } else {\n    swiper.slideTo(slideToIndex);\n  }\n}\nvar slide = {\n  slideTo,\n  slideToLoop,\n  slideNext,\n  slidePrev,\n  slideReset,\n  slideToClosest,\n  slideToClickedSlide\n};\nfunction loopCreate(slideRealIndex, initial) {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || swiper.virtual && swiper.params.virtual.enabled) return;\n  const initSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    slides.forEach((el, index) => {\n      el.setAttribute('data-swiper-slide-index', index);\n    });\n  };\n  const clearBlankSlides = () => {\n    const slides = elementChildren(slidesEl, `.${params.slideBlankClass}`);\n    slides.forEach(el => {\n      el.remove();\n    });\n    if (slides.length > 0) {\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    }\n  };\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (params.loopAddBlankSlides && (params.slidesPerGroup > 1 || gridEnabled)) {\n    clearBlankSlides();\n  }\n  const slidesPerGroup = params.slidesPerGroup * (gridEnabled ? params.grid.rows : 1);\n  const shouldFillGroup = swiper.slides.length % slidesPerGroup !== 0;\n  const shouldFillGrid = gridEnabled && swiper.slides.length % params.grid.rows !== 0;\n  const addBlankSlides = amountOfSlides => {\n    for (let i = 0; i < amountOfSlides; i += 1) {\n      const slideEl = swiper.isElement ? createElement('swiper-slide', [params.slideBlankClass]) : createElement('div', [params.slideClass, params.slideBlankClass]);\n      swiper.slidesEl.append(slideEl);\n    }\n  };\n  if (shouldFillGroup) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = slidesPerGroup - swiper.slides.length % slidesPerGroup;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else if (shouldFillGrid) {\n    if (params.loopAddBlankSlides) {\n      const slidesToAdd = params.grid.rows - swiper.slides.length % params.grid.rows;\n      addBlankSlides(slidesToAdd);\n      swiper.recalcSlides();\n      swiper.updateSlides();\n    } else {\n      showWarning('Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)');\n    }\n    initSlides();\n  } else {\n    initSlides();\n  }\n  swiper.loopFix({\n    slideRealIndex,\n    direction: params.centeredSlides ? undefined : 'next',\n    initial\n  });\n}\nfunction loopFix(_temp) {\n  let {\n    slideRealIndex,\n    slideTo = true,\n    direction,\n    setTranslate,\n    activeSlideIndex,\n    initial,\n    byController,\n    byMousewheel\n  } = _temp === void 0 ? {} : _temp;\n  const swiper = this;\n  if (!swiper.params.loop) return;\n  swiper.emit('beforeLoopFix');\n  const {\n    slides,\n    allowSlidePrev,\n    allowSlideNext,\n    slidesEl,\n    params\n  } = swiper;\n  const {\n    centeredSlides,\n    initialSlide\n  } = params;\n  swiper.allowSlidePrev = true;\n  swiper.allowSlideNext = true;\n  if (swiper.virtual && params.virtual.enabled) {\n    if (slideTo) {\n      if (!params.centeredSlides && swiper.snapIndex === 0) {\n        swiper.slideTo(swiper.virtual.slides.length, 0, false, true);\n      } else if (params.centeredSlides && swiper.snapIndex < params.slidesPerView) {\n        swiper.slideTo(swiper.virtual.slides.length + swiper.snapIndex, 0, false, true);\n      } else if (swiper.snapIndex === swiper.snapGrid.length - 1) {\n        swiper.slideTo(swiper.virtual.slidesBefore, 0, false, true);\n      }\n    }\n    swiper.allowSlidePrev = allowSlidePrev;\n    swiper.allowSlideNext = allowSlideNext;\n    swiper.emit('loopFix');\n    return;\n  }\n  let slidesPerView = params.slidesPerView;\n  if (slidesPerView === 'auto') {\n    slidesPerView = swiper.slidesPerViewDynamic();\n  } else {\n    slidesPerView = Math.ceil(parseFloat(params.slidesPerView, 10));\n    if (centeredSlides && slidesPerView % 2 === 0) {\n      slidesPerView = slidesPerView + 1;\n    }\n  }\n  const slidesPerGroup = params.slidesPerGroupAuto ? slidesPerView : params.slidesPerGroup;\n  let loopedSlides = centeredSlides ? Math.max(slidesPerGroup, Math.ceil(slidesPerView / 2)) : slidesPerGroup;\n  if (loopedSlides % slidesPerGroup !== 0) {\n    loopedSlides += slidesPerGroup - loopedSlides % slidesPerGroup;\n  }\n  loopedSlides += params.loopAdditionalSlides;\n  swiper.loopedSlides = loopedSlides;\n  const gridEnabled = swiper.grid && params.grid && params.grid.rows > 1;\n  if (slides.length < slidesPerView + loopedSlides || swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    showWarning('Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters');\n  } else if (gridEnabled && params.grid.fill === 'row') {\n    showWarning('Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`');\n  }\n  const prependSlidesIndexes = [];\n  const appendSlidesIndexes = [];\n  const cols = gridEnabled ? Math.ceil(slides.length / params.grid.rows) : slides.length;\n  const isInitialOverflow = initial && cols - initialSlide < slidesPerView && !centeredSlides;\n  let activeIndex = isInitialOverflow ? initialSlide : swiper.activeIndex;\n  if (typeof activeSlideIndex === 'undefined') {\n    activeSlideIndex = swiper.getSlideIndex(slides.find(el => el.classList.contains(params.slideActiveClass)));\n  } else {\n    activeIndex = activeSlideIndex;\n  }\n  const isNext = direction === 'next' || !direction;\n  const isPrev = direction === 'prev' || !direction;\n  let slidesPrepended = 0;\n  let slidesAppended = 0;\n  const activeColIndex = gridEnabled ? slides[activeSlideIndex].column : activeSlideIndex;\n  const activeColIndexWithShift = activeColIndex + (centeredSlides && typeof setTranslate === 'undefined' ? -slidesPerView / 2 + 0.5 : 0);\n  // prepend last slides before start\n  if (activeColIndexWithShift < loopedSlides) {\n    slidesPrepended = Math.max(loopedSlides - activeColIndexWithShift, slidesPerGroup);\n    for (let i = 0; i < loopedSlides - activeColIndexWithShift; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        const colIndexToPrepend = cols - index - 1;\n        for (let i = slides.length - 1; i >= 0; i -= 1) {\n          if (slides[i].column === colIndexToPrepend) prependSlidesIndexes.push(i);\n        }\n        // slides.forEach((slide, slideIndex) => {\n        //   if (slide.column === colIndexToPrepend) prependSlidesIndexes.push(slideIndex);\n        // });\n      } else {\n        prependSlidesIndexes.push(cols - index - 1);\n      }\n    }\n  } else if (activeColIndexWithShift + slidesPerView > cols - loopedSlides) {\n    slidesAppended = Math.max(activeColIndexWithShift - (cols - loopedSlides * 2), slidesPerGroup);\n    if (isInitialOverflow) {\n      slidesAppended = Math.max(slidesAppended, slidesPerView - cols + initialSlide + 1);\n    }\n    for (let i = 0; i < slidesAppended; i += 1) {\n      const index = i - Math.floor(i / cols) * cols;\n      if (gridEnabled) {\n        slides.forEach((slide, slideIndex) => {\n          if (slide.column === index) appendSlidesIndexes.push(slideIndex);\n        });\n      } else {\n        appendSlidesIndexes.push(index);\n      }\n    }\n  }\n  swiper.__preventObserver__ = true;\n  requestAnimationFrame(() => {\n    swiper.__preventObserver__ = false;\n  });\n  if (swiper.params.effect === 'cards' && slides.length < slidesPerView + loopedSlides * 2) {\n    if (appendSlidesIndexes.includes(activeSlideIndex)) {\n      appendSlidesIndexes.splice(appendSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n    if (prependSlidesIndexes.includes(activeSlideIndex)) {\n      prependSlidesIndexes.splice(prependSlidesIndexes.indexOf(activeSlideIndex), 1);\n    }\n  }\n  if (isPrev) {\n    prependSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.prepend(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  if (isNext) {\n    appendSlidesIndexes.forEach(index => {\n      slides[index].swiperLoopMoveDOM = true;\n      slidesEl.append(slides[index]);\n      slides[index].swiperLoopMoveDOM = false;\n    });\n  }\n  swiper.recalcSlides();\n  if (params.slidesPerView === 'auto') {\n    swiper.updateSlides();\n  } else if (gridEnabled && (prependSlidesIndexes.length > 0 && isPrev || appendSlidesIndexes.length > 0 && isNext)) {\n    swiper.slides.forEach((slide, slideIndex) => {\n      swiper.grid.updateSlide(slideIndex, slide, swiper.slides);\n    });\n  }\n  if (params.watchSlidesProgress) {\n    swiper.updateSlidesOffset();\n  }\n  if (slideTo) {\n    if (prependSlidesIndexes.length > 0 && isPrev) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex + slidesPrepended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex + Math.ceil(slidesPrepended), 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        if (setTranslate) {\n          const shift = gridEnabled ? prependSlidesIndexes.length / params.grid.rows : prependSlidesIndexes.length;\n          swiper.slideTo(swiper.activeIndex + shift, 0, false, true);\n          swiper.touchEventsData.currentTranslate = swiper.translate;\n        }\n      }\n    } else if (appendSlidesIndexes.length > 0 && isNext) {\n      if (typeof slideRealIndex === 'undefined') {\n        const currentSlideTranslate = swiper.slidesGrid[activeIndex];\n        const newSlideTranslate = swiper.slidesGrid[activeIndex - slidesAppended];\n        const diff = newSlideTranslate - currentSlideTranslate;\n        if (byMousewheel) {\n          swiper.setTranslate(swiper.translate - diff);\n        } else {\n          swiper.slideTo(activeIndex - slidesAppended, 0, false, true);\n          if (setTranslate) {\n            swiper.touchEventsData.startTranslate = swiper.touchEventsData.startTranslate - diff;\n            swiper.touchEventsData.currentTranslate = swiper.touchEventsData.currentTranslate - diff;\n          }\n        }\n      } else {\n        const shift = gridEnabled ? appendSlidesIndexes.length / params.grid.rows : appendSlidesIndexes.length;\n        swiper.slideTo(swiper.activeIndex - shift, 0, false, true);\n      }\n    }\n  }\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.controller && swiper.controller.control && !byController) {\n    const loopParams = {\n      slideRealIndex,\n      direction,\n      setTranslate,\n      activeSlideIndex,\n      byController: true\n    };\n    if (Array.isArray(swiper.controller.control)) {\n      swiper.controller.control.forEach(c => {\n        if (!c.destroyed && c.params.loop) c.loopFix({\n          ...loopParams,\n          slideTo: c.params.slidesPerView === params.slidesPerView ? slideTo : false\n        });\n      });\n    } else if (swiper.controller.control instanceof swiper.constructor && swiper.controller.control.params.loop) {\n      swiper.controller.control.loopFix({\n        ...loopParams,\n        slideTo: swiper.controller.control.params.slidesPerView === params.slidesPerView ? slideTo : false\n      });\n    }\n  }\n  swiper.emit('loopFix');\n}\nfunction loopDestroy() {\n  const swiper = this;\n  const {\n    params,\n    slidesEl\n  } = swiper;\n  if (!params.loop || !slidesEl || swiper.virtual && swiper.params.virtual.enabled) return;\n  swiper.recalcSlides();\n  const newSlidesOrder = [];\n  swiper.slides.forEach(slideEl => {\n    const index = typeof slideEl.swiperSlideIndex === 'undefined' ? slideEl.getAttribute('data-swiper-slide-index') * 1 : slideEl.swiperSlideIndex;\n    newSlidesOrder[index] = slideEl;\n  });\n  swiper.slides.forEach(slideEl => {\n    slideEl.removeAttribute('data-swiper-slide-index');\n  });\n  newSlidesOrder.forEach(slideEl => {\n    slidesEl.append(slideEl);\n  });\n  swiper.recalcSlides();\n  swiper.slideTo(swiper.realIndex, 0);\n}\nvar loop = {\n  loopCreate,\n  loopFix,\n  loopDestroy\n};\nfunction setGrabCursor(moving) {\n  const swiper = this;\n  if (!swiper.params.simulateTouch || swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) return;\n  const el = swiper.params.touchEventsTarget === 'container' ? swiper.el : swiper.wrapperEl;\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  el.style.cursor = 'move';\n  el.style.cursor = moving ? 'grabbing' : 'grab';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\nfunction unsetGrabCursor() {\n  const swiper = this;\n  if (swiper.params.watchOverflow && swiper.isLocked || swiper.params.cssMode) {\n    return;\n  }\n  if (swiper.isElement) {\n    swiper.__preventObserver__ = true;\n  }\n  swiper[swiper.params.touchEventsTarget === 'container' ? 'el' : 'wrapperEl'].style.cursor = '';\n  if (swiper.isElement) {\n    requestAnimationFrame(() => {\n      swiper.__preventObserver__ = false;\n    });\n  }\n}\nvar grabCursor = {\n  setGrabCursor,\n  unsetGrabCursor\n};\n\n// Modified from https://stackoverflow.com/questions/54520554/custom-element-getrootnode-closest-function-crossing-multiple-parent-shadowd\nfunction closestElement(selector, base) {\n  if (base === void 0) {\n    base = this;\n  }\n  function __closestFrom(el) {\n    if (!el || el === getDocument() || el === getWindow()) return null;\n    if (el.assignedSlot) el = el.assignedSlot;\n    const found = el.closest(selector);\n    if (!found && !el.getRootNode) {\n      return null;\n    }\n    return found || __closestFrom(el.getRootNode().host);\n  }\n  return __closestFrom(base);\n}\nfunction preventEdgeSwipe(swiper, event, startX) {\n  const window = getWindow();\n  const {\n    params\n  } = swiper;\n  const edgeSwipeDetection = params.edgeSwipeDetection;\n  const edgeSwipeThreshold = params.edgeSwipeThreshold;\n  if (edgeSwipeDetection && (startX <= edgeSwipeThreshold || startX >= window.innerWidth - edgeSwipeThreshold)) {\n    if (edgeSwipeDetection === 'prevent') {\n      event.preventDefault();\n      return true;\n    }\n    return false;\n  }\n  return true;\n}\nfunction onTouchStart(event) {\n  const swiper = this;\n  const document = getDocument();\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  const data = swiper.touchEventsData;\n  if (e.type === 'pointerdown') {\n    if (data.pointerId !== null && data.pointerId !== e.pointerId) {\n      return;\n    }\n    data.pointerId = e.pointerId;\n  } else if (e.type === 'touchstart' && e.targetTouches.length === 1) {\n    data.touchId = e.targetTouches[0].identifier;\n  }\n  if (e.type === 'touchstart') {\n    // don't proceed touch event\n    preventEdgeSwipe(swiper, e, e.targetTouches[0].pageX);\n    return;\n  }\n  const {\n    params,\n    touches,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (swiper.animating && params.preventInteractionOnTransition) {\n    return;\n  }\n  if (!swiper.animating && params.cssMode && params.loop) {\n    swiper.loopFix();\n  }\n  let targetEl = e.target;\n  if (params.touchEventsTarget === 'wrapper') {\n    if (!elementIsChildOf(targetEl, swiper.wrapperEl)) return;\n  }\n  if ('which' in e && e.which === 3) return;\n  if ('button' in e && e.button > 0) return;\n  if (data.isTouched && data.isMoved) return;\n\n  // change target el for shadow root component\n  const swipingClassHasValue = !!params.noSwipingClass && params.noSwipingClass !== '';\n  // eslint-disable-next-line\n  const eventPath = e.composedPath ? e.composedPath() : e.path;\n  if (swipingClassHasValue && e.target && e.target.shadowRoot && eventPath) {\n    targetEl = eventPath[0];\n  }\n  const noSwipingSelector = params.noSwipingSelector ? params.noSwipingSelector : `.${params.noSwipingClass}`;\n  const isTargetShadow = !!(e.target && e.target.shadowRoot);\n\n  // use closestElement for shadow root element to get the actual closest for nested shadow root element\n  if (params.noSwiping && (isTargetShadow ? closestElement(noSwipingSelector, targetEl) : targetEl.closest(noSwipingSelector))) {\n    swiper.allowClick = true;\n    return;\n  }\n  if (params.swipeHandler) {\n    if (!targetEl.closest(params.swipeHandler)) return;\n  }\n  touches.currentX = e.pageX;\n  touches.currentY = e.pageY;\n  const startX = touches.currentX;\n  const startY = touches.currentY;\n\n  // Do NOT start if iOS edge swipe is detected. Otherwise iOS app cannot swipe-to-go-back anymore\n\n  if (!preventEdgeSwipe(swiper, e, startX)) {\n    return;\n  }\n  Object.assign(data, {\n    isTouched: true,\n    isMoved: false,\n    allowTouchCallbacks: true,\n    isScrolling: undefined,\n    startMoving: undefined\n  });\n  touches.startX = startX;\n  touches.startY = startY;\n  data.touchStartTime = now();\n  swiper.allowClick = true;\n  swiper.updateSize();\n  swiper.swipeDirection = undefined;\n  if (params.threshold > 0) data.allowThresholdMove = false;\n  let preventDefault = true;\n  if (targetEl.matches(data.focusableElements)) {\n    preventDefault = false;\n    if (targetEl.nodeName === 'SELECT') {\n      data.isTouched = false;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== targetEl && (e.pointerType === 'mouse' || e.pointerType !== 'mouse' && !targetEl.matches(data.focusableElements))) {\n    document.activeElement.blur();\n  }\n  const shouldPreventDefault = preventDefault && swiper.allowTouchMove && params.touchStartPreventDefault;\n  if ((params.touchStartForcePreventDefault || shouldPreventDefault) && !targetEl.isContentEditable) {\n    e.preventDefault();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode && swiper.animating && !params.cssMode) {\n    swiper.freeMode.onTouchStart();\n  }\n  swiper.emit('touchStart', e);\n}\nfunction onTouchMove(event) {\n  const document = getDocument();\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && event.pointerType === 'mouse') return;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  if (e.type === 'pointermove') {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    const id = e.pointerId;\n    if (id !== data.pointerId) return;\n  }\n  let targetTouch;\n  if (e.type === 'touchmove') {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  } else {\n    targetTouch = e;\n  }\n  if (!data.isTouched) {\n    if (data.startMoving && data.isScrolling) {\n      swiper.emit('touchMoveOpposite', e);\n    }\n    return;\n  }\n  const pageX = targetTouch.pageX;\n  const pageY = targetTouch.pageY;\n  if (e.preventedByNestedSwiper) {\n    touches.startX = pageX;\n    touches.startY = pageY;\n    return;\n  }\n  if (!swiper.allowTouchMove) {\n    if (!e.target.matches(data.focusableElements)) {\n      swiper.allowClick = false;\n    }\n    if (data.isTouched) {\n      Object.assign(touches, {\n        startX: pageX,\n        startY: pageY,\n        currentX: pageX,\n        currentY: pageY\n      });\n      data.touchStartTime = now();\n    }\n    return;\n  }\n  if (params.touchReleaseOnEdges && !params.loop) {\n    if (swiper.isVertical()) {\n      // Vertical\n      if (pageY < touches.startY && swiper.translate <= swiper.maxTranslate() || pageY > touches.startY && swiper.translate >= swiper.minTranslate()) {\n        data.isTouched = false;\n        data.isMoved = false;\n        return;\n      }\n    } else if (rtl && (pageX > touches.startX && -swiper.translate <= swiper.maxTranslate() || pageX < touches.startX && -swiper.translate >= swiper.minTranslate())) {\n      return;\n    } else if (!rtl && (pageX < touches.startX && swiper.translate <= swiper.maxTranslate() || pageX > touches.startX && swiper.translate >= swiper.minTranslate())) {\n      return;\n    }\n  }\n  if (document.activeElement && document.activeElement.matches(data.focusableElements) && document.activeElement !== e.target && e.pointerType !== 'mouse') {\n    document.activeElement.blur();\n  }\n  if (document.activeElement) {\n    if (e.target === document.activeElement && e.target.matches(data.focusableElements)) {\n      data.isMoved = true;\n      swiper.allowClick = false;\n      return;\n    }\n  }\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchMove', e);\n  }\n  touches.previousX = touches.currentX;\n  touches.previousY = touches.currentY;\n  touches.currentX = pageX;\n  touches.currentY = pageY;\n  const diffX = touches.currentX - touches.startX;\n  const diffY = touches.currentY - touches.startY;\n  if (swiper.params.threshold && Math.sqrt(diffX ** 2 + diffY ** 2) < swiper.params.threshold) return;\n  if (typeof data.isScrolling === 'undefined') {\n    let touchAngle;\n    if (swiper.isHorizontal() && touches.currentY === touches.startY || swiper.isVertical() && touches.currentX === touches.startX) {\n      data.isScrolling = false;\n    } else {\n      // eslint-disable-next-line\n      if (diffX * diffX + diffY * diffY >= 25) {\n        touchAngle = Math.atan2(Math.abs(diffY), Math.abs(diffX)) * 180 / Math.PI;\n        data.isScrolling = swiper.isHorizontal() ? touchAngle > params.touchAngle : 90 - touchAngle > params.touchAngle;\n      }\n    }\n  }\n  if (data.isScrolling) {\n    swiper.emit('touchMoveOpposite', e);\n  }\n  if (typeof data.startMoving === 'undefined') {\n    if (touches.currentX !== touches.startX || touches.currentY !== touches.startY) {\n      data.startMoving = true;\n    }\n  }\n  if (data.isScrolling || e.type === 'touchmove' && data.preventTouchMoveFromPointerMove) {\n    data.isTouched = false;\n    return;\n  }\n  if (!data.startMoving) {\n    return;\n  }\n  swiper.allowClick = false;\n  if (!params.cssMode && e.cancelable) {\n    e.preventDefault();\n  }\n  if (params.touchMoveStopPropagation && !params.nested) {\n    e.stopPropagation();\n  }\n  let diff = swiper.isHorizontal() ? diffX : diffY;\n  let touchesDiff = swiper.isHorizontal() ? touches.currentX - touches.previousX : touches.currentY - touches.previousY;\n  if (params.oneWayMovement) {\n    diff = Math.abs(diff) * (rtl ? 1 : -1);\n    touchesDiff = Math.abs(touchesDiff) * (rtl ? 1 : -1);\n  }\n  touches.diff = diff;\n  diff *= params.touchRatio;\n  if (rtl) {\n    diff = -diff;\n    touchesDiff = -touchesDiff;\n  }\n  const prevTouchesDirection = swiper.touchesDirection;\n  swiper.swipeDirection = diff > 0 ? 'prev' : 'next';\n  swiper.touchesDirection = touchesDiff > 0 ? 'prev' : 'next';\n  const isLoop = swiper.params.loop && !params.cssMode;\n  const allowLoopFix = swiper.touchesDirection === 'next' && swiper.allowSlideNext || swiper.touchesDirection === 'prev' && swiper.allowSlidePrev;\n  if (!data.isMoved) {\n    if (isLoop && allowLoopFix) {\n      swiper.loopFix({\n        direction: swiper.swipeDirection\n      });\n    }\n    data.startTranslate = swiper.getTranslate();\n    swiper.setTransition(0);\n    if (swiper.animating) {\n      const evt = new window.CustomEvent('transitionend', {\n        bubbles: true,\n        cancelable: true,\n        detail: {\n          bySwiperTouchMove: true\n        }\n      });\n      swiper.wrapperEl.dispatchEvent(evt);\n    }\n    data.allowMomentumBounce = false;\n    // Grab Cursor\n    if (params.grabCursor && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n      swiper.setGrabCursor(true);\n    }\n    swiper.emit('sliderFirstMove', e);\n  }\n  let loopFixed;\n  new Date().getTime();\n  if (params._loopSwapReset !== false && data.isMoved && data.allowThresholdMove && prevTouchesDirection !== swiper.touchesDirection && isLoop && allowLoopFix && Math.abs(diff) >= 1) {\n    Object.assign(touches, {\n      startX: pageX,\n      startY: pageY,\n      currentX: pageX,\n      currentY: pageY,\n      startTranslate: data.currentTranslate\n    });\n    data.loopSwapReset = true;\n    data.startTranslate = data.currentTranslate;\n    return;\n  }\n  swiper.emit('sliderMove', e);\n  data.isMoved = true;\n  data.currentTranslate = diff + data.startTranslate;\n  let disableParentSwiper = true;\n  let resistanceRatio = params.resistanceRatio;\n  if (params.touchReleaseOnEdges) {\n    resistanceRatio = 0;\n  }\n  if (diff > 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate > (params.centeredSlides ? swiper.minTranslate() - swiper.slidesSizesGrid[swiper.activeIndex + 1] - (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.activeIndex + 1] + swiper.params.spaceBetween : 0) - swiper.params.spaceBetween : swiper.minTranslate())) {\n      swiper.loopFix({\n        direction: 'prev',\n        setTranslate: true,\n        activeSlideIndex: 0\n      });\n    }\n    if (data.currentTranslate > swiper.minTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.minTranslate() - 1 + (-swiper.minTranslate() + data.startTranslate + diff) ** resistanceRatio;\n      }\n    }\n  } else if (diff < 0) {\n    if (isLoop && allowLoopFix && !loopFixed && data.allowThresholdMove && data.currentTranslate < (params.centeredSlides ? swiper.maxTranslate() + swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween + (params.slidesPerView !== 'auto' && swiper.slides.length - params.slidesPerView >= 2 ? swiper.slidesSizesGrid[swiper.slidesSizesGrid.length - 1] + swiper.params.spaceBetween : 0) : swiper.maxTranslate())) {\n      swiper.loopFix({\n        direction: 'next',\n        setTranslate: true,\n        activeSlideIndex: swiper.slides.length - (params.slidesPerView === 'auto' ? swiper.slidesPerViewDynamic() : Math.ceil(parseFloat(params.slidesPerView, 10)))\n      });\n    }\n    if (data.currentTranslate < swiper.maxTranslate()) {\n      disableParentSwiper = false;\n      if (params.resistance) {\n        data.currentTranslate = swiper.maxTranslate() + 1 - (swiper.maxTranslate() - data.startTranslate - diff) ** resistanceRatio;\n      }\n    }\n  }\n  if (disableParentSwiper) {\n    e.preventedByNestedSwiper = true;\n  }\n\n  // Directions locks\n  if (!swiper.allowSlideNext && swiper.swipeDirection === 'next' && data.currentTranslate < data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && swiper.swipeDirection === 'prev' && data.currentTranslate > data.startTranslate) {\n    data.currentTranslate = data.startTranslate;\n  }\n  if (!swiper.allowSlidePrev && !swiper.allowSlideNext) {\n    data.currentTranslate = data.startTranslate;\n  }\n\n  // Threshold\n  if (params.threshold > 0) {\n    if (Math.abs(diff) > params.threshold || data.allowThresholdMove) {\n      if (!data.allowThresholdMove) {\n        data.allowThresholdMove = true;\n        touches.startX = touches.currentX;\n        touches.startY = touches.currentY;\n        data.currentTranslate = data.startTranslate;\n        touches.diff = swiper.isHorizontal() ? touches.currentX - touches.startX : touches.currentY - touches.startY;\n        return;\n      }\n    } else {\n      data.currentTranslate = data.startTranslate;\n      return;\n    }\n  }\n  if (!params.followFinger || params.cssMode) return;\n\n  // Update active index in free mode\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode || params.watchSlidesProgress) {\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  if (params.freeMode && params.freeMode.enabled && swiper.freeMode) {\n    swiper.freeMode.onTouchMove();\n  }\n  // Update progress\n  swiper.updateProgress(data.currentTranslate);\n  // Update translate\n  swiper.setTranslate(data.currentTranslate);\n}\nfunction onTouchEnd(event) {\n  const swiper = this;\n  const data = swiper.touchEventsData;\n  let e = event;\n  if (e.originalEvent) e = e.originalEvent;\n  let targetTouch;\n  const isTouchEvent = e.type === 'touchend' || e.type === 'touchcancel';\n  if (!isTouchEvent) {\n    if (data.touchId !== null) return; // return from pointer if we use touch\n    if (e.pointerId !== data.pointerId) return;\n    targetTouch = e;\n  } else {\n    targetTouch = [...e.changedTouches].find(t => t.identifier === data.touchId);\n    if (!targetTouch || targetTouch.identifier !== data.touchId) return;\n  }\n  if (['pointercancel', 'pointerout', 'pointerleave', 'contextmenu'].includes(e.type)) {\n    const proceed = ['pointercancel', 'contextmenu'].includes(e.type) && (swiper.browser.isSafari || swiper.browser.isWebView);\n    if (!proceed) {\n      return;\n    }\n  }\n  data.pointerId = null;\n  data.touchId = null;\n  const {\n    params,\n    touches,\n    rtlTranslate: rtl,\n    slidesGrid,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  if (!params.simulateTouch && e.pointerType === 'mouse') return;\n  if (data.allowTouchCallbacks) {\n    swiper.emit('touchEnd', e);\n  }\n  data.allowTouchCallbacks = false;\n  if (!data.isTouched) {\n    if (data.isMoved && params.grabCursor) {\n      swiper.setGrabCursor(false);\n    }\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n\n  // Return Grab Cursor\n  if (params.grabCursor && data.isMoved && data.isTouched && (swiper.allowSlideNext === true || swiper.allowSlidePrev === true)) {\n    swiper.setGrabCursor(false);\n  }\n\n  // Time diff\n  const touchEndTime = now();\n  const timeDiff = touchEndTime - data.touchStartTime;\n\n  // Tap, doubleTap, Click\n  if (swiper.allowClick) {\n    const pathTree = e.path || e.composedPath && e.composedPath();\n    swiper.updateClickedSlide(pathTree && pathTree[0] || e.target, pathTree);\n    swiper.emit('tap click', e);\n    if (timeDiff < 300 && touchEndTime - data.lastClickTime < 300) {\n      swiper.emit('doubleTap doubleClick', e);\n    }\n  }\n  data.lastClickTime = now();\n  nextTick(() => {\n    if (!swiper.destroyed) swiper.allowClick = true;\n  });\n  if (!data.isTouched || !data.isMoved || !swiper.swipeDirection || touches.diff === 0 && !data.loopSwapReset || data.currentTranslate === data.startTranslate && !data.loopSwapReset) {\n    data.isTouched = false;\n    data.isMoved = false;\n    data.startMoving = false;\n    return;\n  }\n  data.isTouched = false;\n  data.isMoved = false;\n  data.startMoving = false;\n  let currentPos;\n  if (params.followFinger) {\n    currentPos = rtl ? swiper.translate : -swiper.translate;\n  } else {\n    currentPos = -data.currentTranslate;\n  }\n  if (params.cssMode) {\n    return;\n  }\n  if (params.freeMode && params.freeMode.enabled) {\n    swiper.freeMode.onTouchEnd({\n      currentPos\n    });\n    return;\n  }\n\n  // Find current slide\n  const swipeToLast = currentPos >= -swiper.maxTranslate() && !swiper.params.loop;\n  let stopIndex = 0;\n  let groupSize = swiper.slidesSizesGrid[0];\n  for (let i = 0; i < slidesGrid.length; i += i < params.slidesPerGroupSkip ? 1 : params.slidesPerGroup) {\n    const increment = i < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n    if (typeof slidesGrid[i + increment] !== 'undefined') {\n      if (swipeToLast || currentPos >= slidesGrid[i] && currentPos < slidesGrid[i + increment]) {\n        stopIndex = i;\n        groupSize = slidesGrid[i + increment] - slidesGrid[i];\n      }\n    } else if (swipeToLast || currentPos >= slidesGrid[i]) {\n      stopIndex = i;\n      groupSize = slidesGrid[slidesGrid.length - 1] - slidesGrid[slidesGrid.length - 2];\n    }\n  }\n  let rewindFirstIndex = null;\n  let rewindLastIndex = null;\n  if (params.rewind) {\n    if (swiper.isBeginning) {\n      rewindLastIndex = params.virtual && params.virtual.enabled && swiper.virtual ? swiper.virtual.slides.length - 1 : swiper.slides.length - 1;\n    } else if (swiper.isEnd) {\n      rewindFirstIndex = 0;\n    }\n  }\n  // Find current slide size\n  const ratio = (currentPos - slidesGrid[stopIndex]) / groupSize;\n  const increment = stopIndex < params.slidesPerGroupSkip - 1 ? 1 : params.slidesPerGroup;\n  if (timeDiff > params.longSwipesMs) {\n    // Long touches\n    if (!params.longSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    if (swiper.swipeDirection === 'next') {\n      if (ratio >= params.longSwipesRatio) swiper.slideTo(params.rewind && swiper.isEnd ? rewindFirstIndex : stopIndex + increment);else swiper.slideTo(stopIndex);\n    }\n    if (swiper.swipeDirection === 'prev') {\n      if (ratio > 1 - params.longSwipesRatio) {\n        swiper.slideTo(stopIndex + increment);\n      } else if (rewindLastIndex !== null && ratio < 0 && Math.abs(ratio) > params.longSwipesRatio) {\n        swiper.slideTo(rewindLastIndex);\n      } else {\n        swiper.slideTo(stopIndex);\n      }\n    }\n  } else {\n    // Short swipes\n    if (!params.shortSwipes) {\n      swiper.slideTo(swiper.activeIndex);\n      return;\n    }\n    const isNavButtonTarget = swiper.navigation && (e.target === swiper.navigation.nextEl || e.target === swiper.navigation.prevEl);\n    if (!isNavButtonTarget) {\n      if (swiper.swipeDirection === 'next') {\n        swiper.slideTo(rewindFirstIndex !== null ? rewindFirstIndex : stopIndex + increment);\n      }\n      if (swiper.swipeDirection === 'prev') {\n        swiper.slideTo(rewindLastIndex !== null ? rewindLastIndex : stopIndex);\n      }\n    } else if (e.target === swiper.navigation.nextEl) {\n      swiper.slideTo(stopIndex + increment);\n    } else {\n      swiper.slideTo(stopIndex);\n    }\n  }\n}\nfunction onResize() {\n  const swiper = this;\n  const {\n    params,\n    el\n  } = swiper;\n  if (el && el.offsetWidth === 0) return;\n\n  // Breakpoints\n  if (params.breakpoints) {\n    swiper.setBreakpoint();\n  }\n\n  // Save locks\n  const {\n    allowSlideNext,\n    allowSlidePrev,\n    snapGrid\n  } = swiper;\n  const isVirtual = swiper.virtual && swiper.params.virtual.enabled;\n\n  // Disable locks on resize\n  swiper.allowSlideNext = true;\n  swiper.allowSlidePrev = true;\n  swiper.updateSize();\n  swiper.updateSlides();\n  swiper.updateSlidesClasses();\n  const isVirtualLoop = isVirtual && params.loop;\n  if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !swiper.isBeginning && !swiper.params.centeredSlides && !isVirtualLoop) {\n    swiper.slideTo(swiper.slides.length - 1, 0, false, true);\n  } else {\n    if (swiper.params.loop && !isVirtual) {\n      swiper.slideToLoop(swiper.realIndex, 0, false, true);\n    } else {\n      swiper.slideTo(swiper.activeIndex, 0, false, true);\n    }\n  }\n  if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n    clearTimeout(swiper.autoplay.resizeTimeout);\n    swiper.autoplay.resizeTimeout = setTimeout(() => {\n      if (swiper.autoplay && swiper.autoplay.running && swiper.autoplay.paused) {\n        swiper.autoplay.resume();\n      }\n    }, 500);\n  }\n  // Return locks after resize\n  swiper.allowSlidePrev = allowSlidePrev;\n  swiper.allowSlideNext = allowSlideNext;\n  if (swiper.params.watchOverflow && snapGrid !== swiper.snapGrid) {\n    swiper.checkOverflow();\n  }\n}\nfunction onClick(e) {\n  const swiper = this;\n  if (!swiper.enabled) return;\n  if (!swiper.allowClick) {\n    if (swiper.params.preventClicks) e.preventDefault();\n    if (swiper.params.preventClicksPropagation && swiper.animating) {\n      e.stopPropagation();\n      e.stopImmediatePropagation();\n    }\n  }\n}\nfunction onScroll() {\n  const swiper = this;\n  const {\n    wrapperEl,\n    rtlTranslate,\n    enabled\n  } = swiper;\n  if (!enabled) return;\n  swiper.previousTranslate = swiper.translate;\n  if (swiper.isHorizontal()) {\n    swiper.translate = -wrapperEl.scrollLeft;\n  } else {\n    swiper.translate = -wrapperEl.scrollTop;\n  }\n  // eslint-disable-next-line\n  if (swiper.translate === 0) swiper.translate = 0;\n  swiper.updateActiveIndex();\n  swiper.updateSlidesClasses();\n  let newProgress;\n  const translatesDiff = swiper.maxTranslate() - swiper.minTranslate();\n  if (translatesDiff === 0) {\n    newProgress = 0;\n  } else {\n    newProgress = (swiper.translate - swiper.minTranslate()) / translatesDiff;\n  }\n  if (newProgress !== swiper.progress) {\n    swiper.updateProgress(rtlTranslate ? -swiper.translate : swiper.translate);\n  }\n  swiper.emit('setTranslate', swiper.translate, false);\n}\nfunction onLoad(e) {\n  const swiper = this;\n  processLazyPreloader(swiper, e.target);\n  if (swiper.params.cssMode || swiper.params.slidesPerView !== 'auto' && !swiper.params.autoHeight) {\n    return;\n  }\n  swiper.update();\n}\nfunction onDocumentTouchStart() {\n  const swiper = this;\n  if (swiper.documentTouchHandlerProceeded) return;\n  swiper.documentTouchHandlerProceeded = true;\n  if (swiper.params.touchReleaseOnEdges) {\n    swiper.el.style.touchAction = 'auto';\n  }\n}\nconst events = (swiper, method) => {\n  const document = getDocument();\n  const {\n    params,\n    el,\n    wrapperEl,\n    device\n  } = swiper;\n  const capture = !!params.nested;\n  const domMethod = method === 'on' ? 'addEventListener' : 'removeEventListener';\n  const swiperMethod = method;\n  if (!el || typeof el === 'string') return;\n\n  // Touch Events\n  document[domMethod]('touchstart', swiper.onDocumentTouchStart, {\n    passive: false,\n    capture\n  });\n  el[domMethod]('touchstart', swiper.onTouchStart, {\n    passive: false\n  });\n  el[domMethod]('pointerdown', swiper.onTouchStart, {\n    passive: false\n  });\n  document[domMethod]('touchmove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('pointermove', swiper.onTouchMove, {\n    passive: false,\n    capture\n  });\n  document[domMethod]('touchend', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerup', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointercancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('touchcancel', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerout', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('pointerleave', swiper.onTouchEnd, {\n    passive: true\n  });\n  document[domMethod]('contextmenu', swiper.onTouchEnd, {\n    passive: true\n  });\n\n  // Prevent Links Clicks\n  if (params.preventClicks || params.preventClicksPropagation) {\n    el[domMethod]('click', swiper.onClick, true);\n  }\n  if (params.cssMode) {\n    wrapperEl[domMethod]('scroll', swiper.onScroll);\n  }\n\n  // Resize handler\n  if (params.updateOnWindowResize) {\n    swiper[swiperMethod](device.ios || device.android ? 'resize orientationchange observerUpdate' : 'resize observerUpdate', onResize, true);\n  } else {\n    swiper[swiperMethod]('observerUpdate', onResize, true);\n  }\n\n  // Images loader\n  el[domMethod]('load', swiper.onLoad, {\n    capture: true\n  });\n};\nfunction attachEvents() {\n  const swiper = this;\n  const {\n    params\n  } = swiper;\n  swiper.onTouchStart = onTouchStart.bind(swiper);\n  swiper.onTouchMove = onTouchMove.bind(swiper);\n  swiper.onTouchEnd = onTouchEnd.bind(swiper);\n  swiper.onDocumentTouchStart = onDocumentTouchStart.bind(swiper);\n  if (params.cssMode) {\n    swiper.onScroll = onScroll.bind(swiper);\n  }\n  swiper.onClick = onClick.bind(swiper);\n  swiper.onLoad = onLoad.bind(swiper);\n  events(swiper, 'on');\n}\nfunction detachEvents() {\n  const swiper = this;\n  events(swiper, 'off');\n}\nvar events$1 = {\n  attachEvents,\n  detachEvents\n};\nconst isGridEnabled = (swiper, params) => {\n  return swiper.grid && params.grid && params.grid.rows > 1;\n};\nfunction setBreakpoint() {\n  const swiper = this;\n  const {\n    realIndex,\n    initialized,\n    params,\n    el\n  } = swiper;\n  const breakpoints = params.breakpoints;\n  if (!breakpoints || breakpoints && Object.keys(breakpoints).length === 0) return;\n  const document = getDocument();\n\n  // Get breakpoint for window/container width and update parameters\n  const breakpointsBase = params.breakpointsBase === 'window' || !params.breakpointsBase ? params.breakpointsBase : 'container';\n  const breakpointContainer = ['window', 'container'].includes(params.breakpointsBase) || !params.breakpointsBase ? swiper.el : document.querySelector(params.breakpointsBase);\n  const breakpoint = swiper.getBreakpoint(breakpoints, breakpointsBase, breakpointContainer);\n  if (!breakpoint || swiper.currentBreakpoint === breakpoint) return;\n  const breakpointOnlyParams = breakpoint in breakpoints ? breakpoints[breakpoint] : undefined;\n  const breakpointParams = breakpointOnlyParams || swiper.originalParams;\n  const wasMultiRow = isGridEnabled(swiper, params);\n  const isMultiRow = isGridEnabled(swiper, breakpointParams);\n  const wasGrabCursor = swiper.params.grabCursor;\n  const isGrabCursor = breakpointParams.grabCursor;\n  const wasEnabled = params.enabled;\n  if (wasMultiRow && !isMultiRow) {\n    el.classList.remove(`${params.containerModifierClass}grid`, `${params.containerModifierClass}grid-column`);\n    swiper.emitContainerClasses();\n  } else if (!wasMultiRow && isMultiRow) {\n    el.classList.add(`${params.containerModifierClass}grid`);\n    if (breakpointParams.grid.fill && breakpointParams.grid.fill === 'column' || !breakpointParams.grid.fill && params.grid.fill === 'column') {\n      el.classList.add(`${params.containerModifierClass}grid-column`);\n    }\n    swiper.emitContainerClasses();\n  }\n  if (wasGrabCursor && !isGrabCursor) {\n    swiper.unsetGrabCursor();\n  } else if (!wasGrabCursor && isGrabCursor) {\n    swiper.setGrabCursor();\n  }\n\n  // Toggle navigation, pagination, scrollbar\n  ['navigation', 'pagination', 'scrollbar'].forEach(prop => {\n    if (typeof breakpointParams[prop] === 'undefined') return;\n    const wasModuleEnabled = params[prop] && params[prop].enabled;\n    const isModuleEnabled = breakpointParams[prop] && breakpointParams[prop].enabled;\n    if (wasModuleEnabled && !isModuleEnabled) {\n      swiper[prop].disable();\n    }\n    if (!wasModuleEnabled && isModuleEnabled) {\n      swiper[prop].enable();\n    }\n  });\n  const directionChanged = breakpointParams.direction && breakpointParams.direction !== params.direction;\n  const needsReLoop = params.loop && (breakpointParams.slidesPerView !== params.slidesPerView || directionChanged);\n  const wasLoop = params.loop;\n  if (directionChanged && initialized) {\n    swiper.changeDirection();\n  }\n  extend(swiper.params, breakpointParams);\n  const isEnabled = swiper.params.enabled;\n  const hasLoop = swiper.params.loop;\n  Object.assign(swiper, {\n    allowTouchMove: swiper.params.allowTouchMove,\n    allowSlideNext: swiper.params.allowSlideNext,\n    allowSlidePrev: swiper.params.allowSlidePrev\n  });\n  if (wasEnabled && !isEnabled) {\n    swiper.disable();\n  } else if (!wasEnabled && isEnabled) {\n    swiper.enable();\n  }\n  swiper.currentBreakpoint = breakpoint;\n  swiper.emit('_beforeBreakpoint', breakpointParams);\n  if (initialized) {\n    if (needsReLoop) {\n      swiper.loopDestroy();\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (!wasLoop && hasLoop) {\n      swiper.loopCreate(realIndex);\n      swiper.updateSlides();\n    } else if (wasLoop && !hasLoop) {\n      swiper.loopDestroy();\n    }\n  }\n  swiper.emit('breakpoint', breakpointParams);\n}\nfunction getBreakpoint(breakpoints, base, containerEl) {\n  if (base === void 0) {\n    base = 'window';\n  }\n  if (!breakpoints || base === 'container' && !containerEl) return undefined;\n  let breakpoint = false;\n  const window = getWindow();\n  const currentHeight = base === 'window' ? window.innerHeight : containerEl.clientHeight;\n  const points = Object.keys(breakpoints).map(point => {\n    if (typeof point === 'string' && point.indexOf('@') === 0) {\n      const minRatio = parseFloat(point.substr(1));\n      const value = currentHeight * minRatio;\n      return {\n        value,\n        point\n      };\n    }\n    return {\n      value: point,\n      point\n    };\n  });\n  points.sort((a, b) => parseInt(a.value, 10) - parseInt(b.value, 10));\n  for (let i = 0; i < points.length; i += 1) {\n    const {\n      point,\n      value\n    } = points[i];\n    if (base === 'window') {\n      if (window.matchMedia(`(min-width: ${value}px)`).matches) {\n        breakpoint = point;\n      }\n    } else if (value <= containerEl.clientWidth) {\n      breakpoint = point;\n    }\n  }\n  return breakpoint || 'max';\n}\nvar breakpoints = {\n  setBreakpoint,\n  getBreakpoint\n};\nfunction prepareClasses(entries, prefix) {\n  const resultClasses = [];\n  entries.forEach(item => {\n    if (typeof item === 'object') {\n      Object.keys(item).forEach(classNames => {\n        if (item[classNames]) {\n          resultClasses.push(prefix + classNames);\n        }\n      });\n    } else if (typeof item === 'string') {\n      resultClasses.push(prefix + item);\n    }\n  });\n  return resultClasses;\n}\nfunction addClasses() {\n  const swiper = this;\n  const {\n    classNames,\n    params,\n    rtl,\n    el,\n    device\n  } = swiper;\n  // prettier-ignore\n  const suffixes = prepareClasses(['initialized', params.direction, {\n    'free-mode': swiper.params.freeMode && params.freeMode.enabled\n  }, {\n    'autoheight': params.autoHeight\n  }, {\n    'rtl': rtl\n  }, {\n    'grid': params.grid && params.grid.rows > 1\n  }, {\n    'grid-column': params.grid && params.grid.rows > 1 && params.grid.fill === 'column'\n  }, {\n    'android': device.android\n  }, {\n    'ios': device.ios\n  }, {\n    'css-mode': params.cssMode\n  }, {\n    'centered': params.cssMode && params.centeredSlides\n  }, {\n    'watch-progress': params.watchSlidesProgress\n  }], params.containerModifierClass);\n  classNames.push(...suffixes);\n  el.classList.add(...classNames);\n  swiper.emitContainerClasses();\n}\nfunction removeClasses() {\n  const swiper = this;\n  const {\n    el,\n    classNames\n  } = swiper;\n  if (!el || typeof el === 'string') return;\n  el.classList.remove(...classNames);\n  swiper.emitContainerClasses();\n}\nvar classes = {\n  addClasses,\n  removeClasses\n};\nfunction checkOverflow() {\n  const swiper = this;\n  const {\n    isLocked: wasLocked,\n    params\n  } = swiper;\n  const {\n    slidesOffsetBefore\n  } = params;\n  if (slidesOffsetBefore) {\n    const lastSlideIndex = swiper.slides.length - 1;\n    const lastSlideRightEdge = swiper.slidesGrid[lastSlideIndex] + swiper.slidesSizesGrid[lastSlideIndex] + slidesOffsetBefore * 2;\n    swiper.isLocked = swiper.size > lastSlideRightEdge;\n  } else {\n    swiper.isLocked = swiper.snapGrid.length === 1;\n  }\n  if (params.allowSlideNext === true) {\n    swiper.allowSlideNext = !swiper.isLocked;\n  }\n  if (params.allowSlidePrev === true) {\n    swiper.allowSlidePrev = !swiper.isLocked;\n  }\n  if (wasLocked && wasLocked !== swiper.isLocked) {\n    swiper.isEnd = false;\n  }\n  if (wasLocked !== swiper.isLocked) {\n    swiper.emit(swiper.isLocked ? 'lock' : 'unlock');\n  }\n}\nvar checkOverflow$1 = {\n  checkOverflow\n};\nvar defaults = {\n  init: true,\n  direction: 'horizontal',\n  oneWayMovement: false,\n  swiperElementNodeName: 'SWIPER-CONTAINER',\n  touchEventsTarget: 'wrapper',\n  initialSlide: 0,\n  speed: 300,\n  cssMode: false,\n  updateOnWindowResize: true,\n  resizeObserver: true,\n  nested: false,\n  createElements: false,\n  eventsPrefix: 'swiper',\n  enabled: true,\n  focusableElements: 'input, select, option, textarea, button, video, label',\n  // Overrides\n  width: null,\n  height: null,\n  //\n  preventInteractionOnTransition: false,\n  // ssr\n  userAgent: null,\n  url: null,\n  // To support iOS's swipe-to-go-back gesture (when being used in-app).\n  edgeSwipeDetection: false,\n  edgeSwipeThreshold: 20,\n  // Autoheight\n  autoHeight: false,\n  // Set wrapper width\n  setWrapperSize: false,\n  // Virtual Translate\n  virtualTranslate: false,\n  // Effects\n  effect: 'slide',\n  // 'slide' or 'fade' or 'cube' or 'coverflow' or 'flip'\n\n  // Breakpoints\n  breakpoints: undefined,\n  breakpointsBase: 'window',\n  // Slides grid\n  spaceBetween: 0,\n  slidesPerView: 1,\n  slidesPerGroup: 1,\n  slidesPerGroupSkip: 0,\n  slidesPerGroupAuto: false,\n  centeredSlides: false,\n  centeredSlidesBounds: false,\n  slidesOffsetBefore: 0,\n  // in px\n  slidesOffsetAfter: 0,\n  // in px\n  normalizeSlideIndex: true,\n  centerInsufficientSlides: false,\n  // Disable swiper and hide navigation when container not overflow\n  watchOverflow: true,\n  // Round length\n  roundLengths: false,\n  // Touches\n  touchRatio: 1,\n  touchAngle: 45,\n  simulateTouch: true,\n  shortSwipes: true,\n  longSwipes: true,\n  longSwipesRatio: 0.5,\n  longSwipesMs: 300,\n  followFinger: true,\n  allowTouchMove: true,\n  threshold: 5,\n  touchMoveStopPropagation: false,\n  touchStartPreventDefault: true,\n  touchStartForcePreventDefault: false,\n  touchReleaseOnEdges: false,\n  // Unique Navigation Elements\n  uniqueNavElements: true,\n  // Resistance\n  resistance: true,\n  resistanceRatio: 0.85,\n  // Progress\n  watchSlidesProgress: false,\n  // Cursor\n  grabCursor: false,\n  // Clicks\n  preventClicks: true,\n  preventClicksPropagation: true,\n  slideToClickedSlide: false,\n  // loop\n  loop: false,\n  loopAddBlankSlides: true,\n  loopAdditionalSlides: 0,\n  loopPreventsSliding: true,\n  // rewind\n  rewind: false,\n  // Swiping/no swiping\n  allowSlidePrev: true,\n  allowSlideNext: true,\n  swipeHandler: null,\n  // '.swipe-handler',\n  noSwiping: true,\n  noSwipingClass: 'swiper-no-swiping',\n  noSwipingSelector: null,\n  // Passive Listeners\n  passiveListeners: true,\n  maxBackfaceHiddenSlides: 10,\n  // NS\n  containerModifierClass: 'swiper-',\n  // NEW\n  slideClass: 'swiper-slide',\n  slideBlankClass: 'swiper-slide-blank',\n  slideActiveClass: 'swiper-slide-active',\n  slideVisibleClass: 'swiper-slide-visible',\n  slideFullyVisibleClass: 'swiper-slide-fully-visible',\n  slideNextClass: 'swiper-slide-next',\n  slidePrevClass: 'swiper-slide-prev',\n  wrapperClass: 'swiper-wrapper',\n  lazyPreloaderClass: 'swiper-lazy-preloader',\n  lazyPreloadPrevNext: 0,\n  // Callbacks\n  runCallbacksOnInit: true,\n  // Internals\n  _emitClasses: false\n};\nfunction moduleExtendParams(params, allModulesParams) {\n  return function extendParams(obj) {\n    if (obj === void 0) {\n      obj = {};\n    }\n    const moduleParamName = Object.keys(obj)[0];\n    const moduleParams = obj[moduleParamName];\n    if (typeof moduleParams !== 'object' || moduleParams === null) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (params[moduleParamName] === true) {\n      params[moduleParamName] = {\n        enabled: true\n      };\n    }\n    if (moduleParamName === 'navigation' && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].prevEl && !params[moduleParamName].nextEl) {\n      params[moduleParamName].auto = true;\n    }\n    if (['pagination', 'scrollbar'].indexOf(moduleParamName) >= 0 && params[moduleParamName] && params[moduleParamName].enabled && !params[moduleParamName].el) {\n      params[moduleParamName].auto = true;\n    }\n    if (!(moduleParamName in params && 'enabled' in moduleParams)) {\n      extend(allModulesParams, obj);\n      return;\n    }\n    if (typeof params[moduleParamName] === 'object' && !('enabled' in params[moduleParamName])) {\n      params[moduleParamName].enabled = true;\n    }\n    if (!params[moduleParamName]) params[moduleParamName] = {\n      enabled: false\n    };\n    extend(allModulesParams, obj);\n  };\n}\n\n/* eslint no-param-reassign: \"off\" */\nconst prototypes = {\n  eventsEmitter,\n  update,\n  translate,\n  transition,\n  slide,\n  loop,\n  grabCursor,\n  events: events$1,\n  breakpoints,\n  checkOverflow: checkOverflow$1,\n  classes\n};\nconst extendedDefaults = {};\nclass Swiper {\n  constructor() {\n    let el;\n    let params;\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (args.length === 1 && args[0].constructor && Object.prototype.toString.call(args[0]).slice(8, -1) === 'Object') {\n      params = args[0];\n    } else {\n      [el, params] = args;\n    }\n    if (!params) params = {};\n    params = extend({}, params);\n    if (el && !params.el) params.el = el;\n    const document = getDocument();\n    if (params.el && typeof params.el === 'string' && document.querySelectorAll(params.el).length > 1) {\n      const swipers = [];\n      document.querySelectorAll(params.el).forEach(containerEl => {\n        const newParams = extend({}, params, {\n          el: containerEl\n        });\n        swipers.push(new Swiper(newParams));\n      });\n      // eslint-disable-next-line no-constructor-return\n      return swipers;\n    }\n\n    // Swiper Instance\n    const swiper = this;\n    swiper.__swiper__ = true;\n    swiper.support = getSupport();\n    swiper.device = getDevice({\n      userAgent: params.userAgent\n    });\n    swiper.browser = getBrowser();\n    swiper.eventsListeners = {};\n    swiper.eventsAnyListeners = [];\n    swiper.modules = [...swiper.__modules__];\n    if (params.modules && Array.isArray(params.modules)) {\n      swiper.modules.push(...params.modules);\n    }\n    const allModulesParams = {};\n    swiper.modules.forEach(mod => {\n      mod({\n        params,\n        swiper,\n        extendParams: moduleExtendParams(params, allModulesParams),\n        on: swiper.on.bind(swiper),\n        once: swiper.once.bind(swiper),\n        off: swiper.off.bind(swiper),\n        emit: swiper.emit.bind(swiper)\n      });\n    });\n\n    // Extend defaults with modules params\n    const swiperParams = extend({}, defaults, allModulesParams);\n\n    // Extend defaults with passed params\n    swiper.params = extend({}, swiperParams, extendedDefaults, params);\n    swiper.originalParams = extend({}, swiper.params);\n    swiper.passedParams = extend({}, params);\n\n    // add event listeners\n    if (swiper.params && swiper.params.on) {\n      Object.keys(swiper.params.on).forEach(eventName => {\n        swiper.on(eventName, swiper.params.on[eventName]);\n      });\n    }\n    if (swiper.params && swiper.params.onAny) {\n      swiper.onAny(swiper.params.onAny);\n    }\n\n    // Extend Swiper\n    Object.assign(swiper, {\n      enabled: swiper.params.enabled,\n      el,\n      // Classes\n      classNames: [],\n      // Slides\n      slides: [],\n      slidesGrid: [],\n      snapGrid: [],\n      slidesSizesGrid: [],\n      // isDirection\n      isHorizontal() {\n        return swiper.params.direction === 'horizontal';\n      },\n      isVertical() {\n        return swiper.params.direction === 'vertical';\n      },\n      // Indexes\n      activeIndex: 0,\n      realIndex: 0,\n      //\n      isBeginning: true,\n      isEnd: false,\n      // Props\n      translate: 0,\n      previousTranslate: 0,\n      progress: 0,\n      velocity: 0,\n      animating: false,\n      cssOverflowAdjustment() {\n        // Returns 0 unless `translate` is > 2**23\n        // Should be subtracted from css values to prevent overflow\n        return Math.trunc(this.translate / 2 ** 23) * 2 ** 23;\n      },\n      // Locks\n      allowSlideNext: swiper.params.allowSlideNext,\n      allowSlidePrev: swiper.params.allowSlidePrev,\n      // Touch Events\n      touchEventsData: {\n        isTouched: undefined,\n        isMoved: undefined,\n        allowTouchCallbacks: undefined,\n        touchStartTime: undefined,\n        isScrolling: undefined,\n        currentTranslate: undefined,\n        startTranslate: undefined,\n        allowThresholdMove: undefined,\n        // Form elements to match\n        focusableElements: swiper.params.focusableElements,\n        // Last click time\n        lastClickTime: 0,\n        clickTimeout: undefined,\n        // Velocities\n        velocities: [],\n        allowMomentumBounce: undefined,\n        startMoving: undefined,\n        pointerId: null,\n        touchId: null\n      },\n      // Clicks\n      allowClick: true,\n      // Touches\n      allowTouchMove: swiper.params.allowTouchMove,\n      touches: {\n        startX: 0,\n        startY: 0,\n        currentX: 0,\n        currentY: 0,\n        diff: 0\n      },\n      // Images\n      imagesToLoad: [],\n      imagesLoaded: 0\n    });\n    swiper.emit('_swiper');\n\n    // Init\n    if (swiper.params.init) {\n      swiper.init();\n    }\n\n    // Return app instance\n    // eslint-disable-next-line no-constructor-return\n    return swiper;\n  }\n  getDirectionLabel(property) {\n    if (this.isHorizontal()) {\n      return property;\n    }\n    // prettier-ignore\n    return {\n      'width': 'height',\n      'margin-top': 'margin-left',\n      'margin-bottom ': 'margin-right',\n      'margin-left': 'margin-top',\n      'margin-right': 'margin-bottom',\n      'padding-left': 'padding-top',\n      'padding-right': 'padding-bottom',\n      'marginRight': 'marginBottom'\n    }[property];\n  }\n  getSlideIndex(slideEl) {\n    const {\n      slidesEl,\n      params\n    } = this;\n    const slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n    const firstSlideIndex = elementIndex(slides[0]);\n    return elementIndex(slideEl) - firstSlideIndex;\n  }\n  getSlideIndexByData(index) {\n    return this.getSlideIndex(this.slides.find(slideEl => slideEl.getAttribute('data-swiper-slide-index') * 1 === index));\n  }\n  getSlideIndexWhenGrid(index) {\n    if (this.grid && this.params.grid && this.params.grid.rows > 1) {\n      if (this.params.grid.fill === 'column') {\n        index = Math.floor(index / this.params.grid.rows);\n      } else if (this.params.grid.fill === 'row') {\n        index = index % Math.ceil(this.slides.length / this.params.grid.rows);\n      }\n    }\n    return index;\n  }\n  recalcSlides() {\n    const swiper = this;\n    const {\n      slidesEl,\n      params\n    } = swiper;\n    swiper.slides = elementChildren(slidesEl, `.${params.slideClass}, swiper-slide`);\n  }\n  enable() {\n    const swiper = this;\n    if (swiper.enabled) return;\n    swiper.enabled = true;\n    if (swiper.params.grabCursor) {\n      swiper.setGrabCursor();\n    }\n    swiper.emit('enable');\n  }\n  disable() {\n    const swiper = this;\n    if (!swiper.enabled) return;\n    swiper.enabled = false;\n    if (swiper.params.grabCursor) {\n      swiper.unsetGrabCursor();\n    }\n    swiper.emit('disable');\n  }\n  setProgress(progress, speed) {\n    const swiper = this;\n    progress = Math.min(Math.max(progress, 0), 1);\n    const min = swiper.minTranslate();\n    const max = swiper.maxTranslate();\n    const current = (max - min) * progress + min;\n    swiper.translateTo(current, typeof speed === 'undefined' ? 0 : speed);\n    swiper.updateActiveIndex();\n    swiper.updateSlidesClasses();\n  }\n  emitContainerClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const cls = swiper.el.className.split(' ').filter(className => {\n      return className.indexOf('swiper') === 0 || className.indexOf(swiper.params.containerModifierClass) === 0;\n    });\n    swiper.emit('_containerClasses', cls.join(' '));\n  }\n  getSlideClasses(slideEl) {\n    const swiper = this;\n    if (swiper.destroyed) return '';\n    return slideEl.className.split(' ').filter(className => {\n      return className.indexOf('swiper-slide') === 0 || className.indexOf(swiper.params.slideClass) === 0;\n    }).join(' ');\n  }\n  emitSlidesClasses() {\n    const swiper = this;\n    if (!swiper.params._emitClasses || !swiper.el) return;\n    const updates = [];\n    swiper.slides.forEach(slideEl => {\n      const classNames = swiper.getSlideClasses(slideEl);\n      updates.push({\n        slideEl,\n        classNames\n      });\n      swiper.emit('_slideClass', slideEl, classNames);\n    });\n    swiper.emit('_slideClasses', updates);\n  }\n  slidesPerViewDynamic(view, exact) {\n    if (view === void 0) {\n      view = 'current';\n    }\n    if (exact === void 0) {\n      exact = false;\n    }\n    const swiper = this;\n    const {\n      params,\n      slides,\n      slidesGrid,\n      slidesSizesGrid,\n      size: swiperSize,\n      activeIndex\n    } = swiper;\n    let spv = 1;\n    if (typeof params.slidesPerView === 'number') return params.slidesPerView;\n    if (params.centeredSlides) {\n      let slideSize = slides[activeIndex] ? Math.ceil(slides[activeIndex].swiperSlideSize) : 0;\n      let breakLoop;\n      for (let i = activeIndex + 1; i < slides.length; i += 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += Math.ceil(slides[i].swiperSlideSize);\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n      for (let i = activeIndex - 1; i >= 0; i -= 1) {\n        if (slides[i] && !breakLoop) {\n          slideSize += slides[i].swiperSlideSize;\n          spv += 1;\n          if (slideSize > swiperSize) breakLoop = true;\n        }\n      }\n    } else {\n      // eslint-disable-next-line\n      if (view === 'current') {\n        for (let i = activeIndex + 1; i < slides.length; i += 1) {\n          const slideInView = exact ? slidesGrid[i] + slidesSizesGrid[i] - slidesGrid[activeIndex] < swiperSize : slidesGrid[i] - slidesGrid[activeIndex] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      } else {\n        // previous\n        for (let i = activeIndex - 1; i >= 0; i -= 1) {\n          const slideInView = slidesGrid[activeIndex] - slidesGrid[i] < swiperSize;\n          if (slideInView) {\n            spv += 1;\n          }\n        }\n      }\n    }\n    return spv;\n  }\n  update() {\n    const swiper = this;\n    if (!swiper || swiper.destroyed) return;\n    const {\n      snapGrid,\n      params\n    } = swiper;\n    // Breakpoints\n    if (params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n    [...swiper.el.querySelectorAll('[loading=\"lazy\"]')].forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      }\n    });\n    swiper.updateSize();\n    swiper.updateSlides();\n    swiper.updateProgress();\n    swiper.updateSlidesClasses();\n    function setTranslate() {\n      const translateValue = swiper.rtlTranslate ? swiper.translate * -1 : swiper.translate;\n      const newTranslate = Math.min(Math.max(translateValue, swiper.maxTranslate()), swiper.minTranslate());\n      swiper.setTranslate(newTranslate);\n      swiper.updateActiveIndex();\n      swiper.updateSlidesClasses();\n    }\n    let translated;\n    if (params.freeMode && params.freeMode.enabled && !params.cssMode) {\n      setTranslate();\n      if (params.autoHeight) {\n        swiper.updateAutoHeight();\n      }\n    } else {\n      if ((params.slidesPerView === 'auto' || params.slidesPerView > 1) && swiper.isEnd && !params.centeredSlides) {\n        const slides = swiper.virtual && params.virtual.enabled ? swiper.virtual.slides : swiper.slides;\n        translated = swiper.slideTo(slides.length - 1, 0, false, true);\n      } else {\n        translated = swiper.slideTo(swiper.activeIndex, 0, false, true);\n      }\n      if (!translated) {\n        setTranslate();\n      }\n    }\n    if (params.watchOverflow && snapGrid !== swiper.snapGrid) {\n      swiper.checkOverflow();\n    }\n    swiper.emit('update');\n  }\n  changeDirection(newDirection, needUpdate) {\n    if (needUpdate === void 0) {\n      needUpdate = true;\n    }\n    const swiper = this;\n    const currentDirection = swiper.params.direction;\n    if (!newDirection) {\n      // eslint-disable-next-line\n      newDirection = currentDirection === 'horizontal' ? 'vertical' : 'horizontal';\n    }\n    if (newDirection === currentDirection || newDirection !== 'horizontal' && newDirection !== 'vertical') {\n      return swiper;\n    }\n    swiper.el.classList.remove(`${swiper.params.containerModifierClass}${currentDirection}`);\n    swiper.el.classList.add(`${swiper.params.containerModifierClass}${newDirection}`);\n    swiper.emitContainerClasses();\n    swiper.params.direction = newDirection;\n    swiper.slides.forEach(slideEl => {\n      if (newDirection === 'vertical') {\n        slideEl.style.width = '';\n      } else {\n        slideEl.style.height = '';\n      }\n    });\n    swiper.emit('changeDirection');\n    if (needUpdate) swiper.update();\n    return swiper;\n  }\n  changeLanguageDirection(direction) {\n    const swiper = this;\n    if (swiper.rtl && direction === 'rtl' || !swiper.rtl && direction === 'ltr') return;\n    swiper.rtl = direction === 'rtl';\n    swiper.rtlTranslate = swiper.params.direction === 'horizontal' && swiper.rtl;\n    if (swiper.rtl) {\n      swiper.el.classList.add(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'rtl';\n    } else {\n      swiper.el.classList.remove(`${swiper.params.containerModifierClass}rtl`);\n      swiper.el.dir = 'ltr';\n    }\n    swiper.update();\n  }\n  mount(element) {\n    const swiper = this;\n    if (swiper.mounted) return true;\n\n    // Find el\n    let el = element || swiper.params.el;\n    if (typeof el === 'string') {\n      el = document.querySelector(el);\n    }\n    if (!el) {\n      return false;\n    }\n    el.swiper = swiper;\n    if (el.parentNode && el.parentNode.host && el.parentNode.host.nodeName === swiper.params.swiperElementNodeName.toUpperCase()) {\n      swiper.isElement = true;\n    }\n    const getWrapperSelector = () => {\n      return `.${(swiper.params.wrapperClass || '').trim().split(' ').join('.')}`;\n    };\n    const getWrapper = () => {\n      if (el && el.shadowRoot && el.shadowRoot.querySelector) {\n        const res = el.shadowRoot.querySelector(getWrapperSelector());\n        // Children needs to return slot items\n        return res;\n      }\n      return elementChildren(el, getWrapperSelector())[0];\n    };\n    // Find Wrapper\n    let wrapperEl = getWrapper();\n    if (!wrapperEl && swiper.params.createElements) {\n      wrapperEl = createElement('div', swiper.params.wrapperClass);\n      el.append(wrapperEl);\n      elementChildren(el, `.${swiper.params.slideClass}`).forEach(slideEl => {\n        wrapperEl.append(slideEl);\n      });\n    }\n    Object.assign(swiper, {\n      el,\n      wrapperEl,\n      slidesEl: swiper.isElement && !el.parentNode.host.slideSlots ? el.parentNode.host : wrapperEl,\n      hostEl: swiper.isElement ? el.parentNode.host : el,\n      mounted: true,\n      // RTL\n      rtl: el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl',\n      rtlTranslate: swiper.params.direction === 'horizontal' && (el.dir.toLowerCase() === 'rtl' || elementStyle(el, 'direction') === 'rtl'),\n      wrongRTL: elementStyle(wrapperEl, 'display') === '-webkit-box'\n    });\n    return true;\n  }\n  init(el) {\n    const swiper = this;\n    if (swiper.initialized) return swiper;\n    const mounted = swiper.mount(el);\n    if (mounted === false) return swiper;\n    swiper.emit('beforeInit');\n\n    // Set breakpoint\n    if (swiper.params.breakpoints) {\n      swiper.setBreakpoint();\n    }\n\n    // Add Classes\n    swiper.addClasses();\n\n    // Update size\n    swiper.updateSize();\n\n    // Update slides\n    swiper.updateSlides();\n    if (swiper.params.watchOverflow) {\n      swiper.checkOverflow();\n    }\n\n    // Set Grab Cursor\n    if (swiper.params.grabCursor && swiper.enabled) {\n      swiper.setGrabCursor();\n    }\n\n    // Slide To Initial Slide\n    if (swiper.params.loop && swiper.virtual && swiper.params.virtual.enabled) {\n      swiper.slideTo(swiper.params.initialSlide + swiper.virtual.slidesBefore, 0, swiper.params.runCallbacksOnInit, false, true);\n    } else {\n      swiper.slideTo(swiper.params.initialSlide, 0, swiper.params.runCallbacksOnInit, false, true);\n    }\n\n    // Create loop\n    if (swiper.params.loop) {\n      swiper.loopCreate(undefined, true);\n    }\n\n    // Attach events\n    swiper.attachEvents();\n    const lazyElements = [...swiper.el.querySelectorAll('[loading=\"lazy\"]')];\n    if (swiper.isElement) {\n      lazyElements.push(...swiper.hostEl.querySelectorAll('[loading=\"lazy\"]'));\n    }\n    lazyElements.forEach(imageEl => {\n      if (imageEl.complete) {\n        processLazyPreloader(swiper, imageEl);\n      } else {\n        imageEl.addEventListener('load', e => {\n          processLazyPreloader(swiper, e.target);\n        });\n      }\n    });\n    preload(swiper);\n\n    // Init Flag\n    swiper.initialized = true;\n    preload(swiper);\n\n    // Emit\n    swiper.emit('init');\n    swiper.emit('afterInit');\n    return swiper;\n  }\n  destroy(deleteInstance, cleanStyles) {\n    if (deleteInstance === void 0) {\n      deleteInstance = true;\n    }\n    if (cleanStyles === void 0) {\n      cleanStyles = true;\n    }\n    const swiper = this;\n    const {\n      params,\n      el,\n      wrapperEl,\n      slides\n    } = swiper;\n    if (typeof swiper.params === 'undefined' || swiper.destroyed) {\n      return null;\n    }\n    swiper.emit('beforeDestroy');\n\n    // Init Flag\n    swiper.initialized = false;\n\n    // Detach events\n    swiper.detachEvents();\n\n    // Destroy loop\n    if (params.loop) {\n      swiper.loopDestroy();\n    }\n\n    // Cleanup styles\n    if (cleanStyles) {\n      swiper.removeClasses();\n      if (el && typeof el !== 'string') {\n        el.removeAttribute('style');\n      }\n      if (wrapperEl) {\n        wrapperEl.removeAttribute('style');\n      }\n      if (slides && slides.length) {\n        slides.forEach(slideEl => {\n          slideEl.classList.remove(params.slideVisibleClass, params.slideFullyVisibleClass, params.slideActiveClass, params.slideNextClass, params.slidePrevClass);\n          slideEl.removeAttribute('style');\n          slideEl.removeAttribute('data-swiper-slide-index');\n        });\n      }\n    }\n    swiper.emit('destroy');\n\n    // Detach emitter events\n    Object.keys(swiper.eventsListeners).forEach(eventName => {\n      swiper.off(eventName);\n    });\n    if (deleteInstance !== false) {\n      if (swiper.el && typeof swiper.el !== 'string') {\n        swiper.el.swiper = null;\n      }\n      deleteProps(swiper);\n    }\n    swiper.destroyed = true;\n    return null;\n  }\n  static extendDefaults(newDefaults) {\n    extend(extendedDefaults, newDefaults);\n  }\n  static get extendedDefaults() {\n    return extendedDefaults;\n  }\n  static get defaults() {\n    return defaults;\n  }\n  static installModule(mod) {\n    if (!Swiper.prototype.__modules__) Swiper.prototype.__modules__ = [];\n    const modules = Swiper.prototype.__modules__;\n    if (typeof mod === 'function' && modules.indexOf(mod) < 0) {\n      modules.push(mod);\n    }\n  }\n  static use(module) {\n    if (Array.isArray(module)) {\n      module.forEach(m => Swiper.installModule(m));\n      return Swiper;\n    }\n    Swiper.installModule(module);\n    return Swiper;\n  }\n}\nObject.keys(prototypes).forEach(prototypeGroup => {\n  Object.keys(prototypes[prototypeGroup]).forEach(protoMethod => {\n    Swiper.prototype[protoMethod] = prototypes[prototypeGroup][protoMethod];\n  });\n});\nSwiper.use([Resize, Observer]);\nexport { Swiper as S, defaults as d };"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAI;AACJ,SAAS,cAAc;AACrB,QAAMA,UAAS,UAAU;AACzB,QAAMC,YAAW,YAAY;AAC7B,SAAO;AAAA,IACL,cAAcA,UAAS,mBAAmBA,UAAS,gBAAgB,SAAS,oBAAoBA,UAAS,gBAAgB;AAAA,IACzH,OAAO,CAAC,EAAE,kBAAkBD,WAAUA,QAAO,iBAAiBC,qBAAoBD,QAAO;AAAA,EAC3F;AACF;AACA,SAAS,aAAa;AACpB,MAAI,CAAC,SAAS;AACZ,cAAU,YAAY;AAAA,EACxB;AACA,SAAO;AACT;AACA,IAAI;AACJ,SAAS,WAAW,OAAO;AACzB,MAAI;AAAA,IACF;AAAA,EACF,IAAI,UAAU,SAAS,CAAC,IAAI;AAC5B,QAAME,WAAU,WAAW;AAC3B,QAAMF,UAAS,UAAU;AACzB,QAAM,WAAWA,QAAO,UAAU;AAClC,QAAM,KAAK,aAAaA,QAAO,UAAU;AACzC,QAAM,SAAS;AAAA,IACb,KAAK;AAAA,IACL,SAAS;AAAA,EACX;AACA,QAAM,cAAcA,QAAO,OAAO;AAClC,QAAM,eAAeA,QAAO,OAAO;AACnC,QAAM,UAAU,GAAG,MAAM,6BAA6B;AACtD,MAAI,OAAO,GAAG,MAAM,sBAAsB;AAC1C,QAAM,OAAO,GAAG,MAAM,yBAAyB;AAC/C,QAAM,SAAS,CAAC,QAAQ,GAAG,MAAM,4BAA4B;AAC7D,QAAM,UAAU,aAAa;AAC7B,MAAI,QAAQ,aAAa;AAGzB,QAAM,cAAc,CAAC,aAAa,aAAa,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,YAAY,UAAU;AACrK,MAAI,CAAC,QAAQ,SAASE,SAAQ,SAAS,YAAY,QAAQ,GAAG,WAAW,IAAI,YAAY,EAAE,KAAK,GAAG;AACjG,WAAO,GAAG,MAAM,qBAAqB;AACrC,QAAI,CAAC,KAAM,QAAO,CAAC,GAAG,GAAG,QAAQ;AACjC,YAAQ;AAAA,EACV;AAGA,MAAI,WAAW,CAAC,SAAS;AACvB,WAAO,KAAK;AACZ,WAAO,UAAU;AAAA,EACnB;AACA,MAAI,QAAQ,UAAU,MAAM;AAC1B,WAAO,KAAK;AACZ,WAAO,MAAM;AAAA,EACf;AAGA,SAAO;AACT;AACA,SAAS,UAAU,WAAW;AAC5B,MAAI,cAAc,QAAQ;AACxB,gBAAY,CAAC;AAAA,EACf;AACA,MAAI,CAAC,cAAc;AACjB,mBAAe,WAAW,SAAS;AAAA,EACrC;AACA,SAAO;AACT;AACA,IAAI;AACJ,SAAS,cAAc;AACrB,QAAMF,UAAS,UAAU;AACzB,QAAM,SAAS,UAAU;AACzB,MAAI,qBAAqB;AACzB,WAAS,WAAW;AAClB,UAAM,KAAKA,QAAO,UAAU,UAAU,YAAY;AAClD,WAAO,GAAG,QAAQ,QAAQ,KAAK,KAAK,GAAG,QAAQ,QAAQ,IAAI,KAAK,GAAG,QAAQ,SAAS,IAAI;AAAA,EAC1F;AACA,MAAI,SAAS,GAAG;AACd,UAAM,KAAK,OAAOA,QAAO,UAAU,SAAS;AAC5C,QAAI,GAAG,SAAS,UAAU,GAAG;AAC3B,YAAM,CAAC,OAAO,KAAK,IAAI,GAAG,MAAM,UAAU,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,CAAC,EAAE,MAAM,GAAG,EAAE,IAAI,SAAO,OAAO,GAAG,CAAC;AAC9F,2BAAqB,QAAQ,MAAM,UAAU,MAAM,QAAQ;AAAA,IAC7D;AAAA,EACF;AACA,QAAM,YAAY,+CAA+C,KAAKA,QAAO,UAAU,SAAS;AAChG,QAAM,kBAAkB,SAAS;AACjC,QAAM,YAAY,mBAAmB,aAAa,OAAO;AACzD,SAAO;AAAA,IACL,UAAU,sBAAsB;AAAA,IAChC;AAAA,IACA;AAAA,IACA;AAAA,EACF;AACF;AACA,SAAS,aAAa;AACpB,MAAI,CAAC,SAAS;AACZ,cAAU,YAAY;AAAA,EACxB;AACA,SAAO;AACT;AACA,SAAS,OAAO,MAAM;AACpB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAMA,UAAS,UAAU;AACzB,MAAI,WAAW;AACf,MAAI,iBAAiB;AACrB,QAAM,gBAAgB,MAAM;AAC1B,QAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO,YAAa;AACxD,SAAK,cAAc;AACnB,SAAK,QAAQ;AAAA,EACf;AACA,QAAM,iBAAiB,MAAM;AAC3B,QAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO,YAAa;AACxD,eAAW,IAAI,eAAe,aAAW;AACvC,uBAAiBA,QAAO,sBAAsB,MAAM;AAClD,cAAM;AAAA,UACJ;AAAA,UACA;AAAA,QACF,IAAI;AACJ,YAAI,WAAW;AACf,YAAI,YAAY;AAChB,gBAAQ,QAAQ,WAAS;AACvB,cAAI;AAAA,YACF;AAAA,YACA;AAAA,YACA;AAAA,UACF,IAAI;AACJ,cAAI,UAAU,WAAW,OAAO,GAAI;AACpC,qBAAW,cAAc,YAAY,SAAS,eAAe,CAAC,KAAK,gBAAgB;AACnF,sBAAY,cAAc,YAAY,UAAU,eAAe,CAAC,KAAK,gBAAgB;AAAA,QACvF,CAAC;AACD,YAAI,aAAa,SAAS,cAAc,QAAQ;AAC9C,wBAAc;AAAA,QAChB;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AACD,aAAS,QAAQ,OAAO,EAAE;AAAA,EAC5B;AACA,QAAM,iBAAiB,MAAM;AAC3B,QAAI,gBAAgB;AAClB,MAAAA,QAAO,qBAAqB,cAAc;AAAA,IAC5C;AACA,QAAI,YAAY,SAAS,aAAa,OAAO,IAAI;AAC/C,eAAS,UAAU,OAAO,EAAE;AAC5B,iBAAW;AAAA,IACb;AAAA,EACF;AACA,QAAM,2BAA2B,MAAM;AACrC,QAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO,YAAa;AACxD,SAAK,mBAAmB;AAAA,EAC1B;AACA,KAAG,QAAQ,MAAM;AACf,QAAI,OAAO,OAAO,kBAAkB,OAAOA,QAAO,mBAAmB,aAAa;AAChF,qBAAe;AACf;AAAA,IACF;AACA,IAAAA,QAAO,iBAAiB,UAAU,aAAa;AAC/C,IAAAA,QAAO,iBAAiB,qBAAqB,wBAAwB;AAAA,EACvE,CAAC;AACD,KAAG,WAAW,MAAM;AAClB,mBAAe;AACf,IAAAA,QAAO,oBAAoB,UAAU,aAAa;AAClD,IAAAA,QAAO,oBAAoB,qBAAqB,wBAAwB;AAAA,EAC1E,CAAC;AACH;AACA,SAAS,SAAS,MAAM;AACtB,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,CAAC;AACnB,QAAMA,UAAS,UAAU;AACzB,QAAM,SAAS,SAAU,QAAQ,SAAS;AACxC,QAAI,YAAY,QAAQ;AACtB,gBAAU,CAAC;AAAA,IACb;AACA,UAAM,eAAeA,QAAO,oBAAoBA,QAAO;AACvD,UAAM,WAAW,IAAI,aAAa,eAAa;AAI7C,UAAI,OAAO,oBAAqB;AAChC,UAAI,UAAU,WAAW,GAAG;AAC1B,aAAK,kBAAkB,UAAU,CAAC,CAAC;AACnC;AAAA,MACF;AACA,YAAM,iBAAiB,SAASG,kBAAiB;AAC/C,aAAK,kBAAkB,UAAU,CAAC,CAAC;AAAA,MACrC;AACA,UAAIH,QAAO,uBAAuB;AAChC,QAAAA,QAAO,sBAAsB,cAAc;AAAA,MAC7C,OAAO;AACL,QAAAA,QAAO,WAAW,gBAAgB,CAAC;AAAA,MACrC;AAAA,IACF,CAAC;AACD,aAAS,QAAQ,QAAQ;AAAA,MACvB,YAAY,OAAO,QAAQ,eAAe,cAAc,OAAO,QAAQ;AAAA,MACvE,WAAW,OAAO,cAAc,OAAO,QAAQ,cAAc,cAAc,OAAO,SAAS;AAAA,MAC3F,eAAe,OAAO,QAAQ,kBAAkB,cAAc,OAAO,QAAQ;AAAA,IAC/E,CAAC;AACD,cAAU,KAAK,QAAQ;AAAA,EACzB;AACA,QAAM,OAAO,MAAM;AACjB,QAAI,CAAC,OAAO,OAAO,SAAU;AAC7B,QAAI,OAAO,OAAO,gBAAgB;AAChC,YAAM,mBAAmB,eAAe,OAAO,MAAM;AACrD,eAAS,IAAI,GAAG,IAAI,iBAAiB,QAAQ,KAAK,GAAG;AACnD,eAAO,iBAAiB,CAAC,CAAC;AAAA,MAC5B;AAAA,IACF;AAEA,WAAO,OAAO,QAAQ;AAAA,MACpB,WAAW,OAAO,OAAO;AAAA,IAC3B,CAAC;AAGD,WAAO,OAAO,WAAW;AAAA,MACvB,YAAY;AAAA,IACd,CAAC;AAAA,EACH;AACA,QAAM,UAAU,MAAM;AACpB,cAAU,QAAQ,cAAY;AAC5B,eAAS,WAAW;AAAA,IACtB,CAAC;AACD,cAAU,OAAO,GAAG,UAAU,MAAM;AAAA,EACtC;AACA,eAAa;AAAA,IACX,UAAU;AAAA,IACV,gBAAgB;AAAA,IAChB,sBAAsB;AAAA,EACxB,CAAC;AACD,KAAG,QAAQ,IAAI;AACf,KAAG,WAAW,OAAO;AACvB;AAIA,IAAI,gBAAgB;AAAA,EAClB,GAAGI,SAAQ,SAAS,UAAU;AAC5B,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK,UAAW,QAAO;AACpD,QAAI,OAAO,YAAY,WAAY,QAAO;AAC1C,UAAM,SAAS,WAAW,YAAY;AACtC,IAAAA,QAAO,MAAM,GAAG,EAAE,QAAQ,WAAS;AACjC,UAAI,CAAC,KAAK,gBAAgB,KAAK,EAAG,MAAK,gBAAgB,KAAK,IAAI,CAAC;AACjE,WAAK,gBAAgB,KAAK,EAAE,MAAM,EAAE,OAAO;AAAA,IAC7C,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,KAAKA,SAAQ,SAAS,UAAU;AAC9B,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK,UAAW,QAAO;AACpD,QAAI,OAAO,YAAY,WAAY,QAAO;AAC1C,aAAS,cAAc;AACrB,WAAK,IAAIA,SAAQ,WAAW;AAC5B,UAAI,YAAY,gBAAgB;AAC9B,eAAO,YAAY;AAAA,MACrB;AACA,eAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,aAAK,IAAI,IAAI,UAAU,IAAI;AAAA,MAC7B;AACA,cAAQ,MAAM,MAAM,IAAI;AAAA,IAC1B;AACA,gBAAY,iBAAiB;AAC7B,WAAO,KAAK,GAAGA,SAAQ,aAAa,QAAQ;AAAA,EAC9C;AAAA,EACA,MAAM,SAAS,UAAU;AACvB,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK,UAAW,QAAO;AACpD,QAAI,OAAO,YAAY,WAAY,QAAO;AAC1C,UAAM,SAAS,WAAW,YAAY;AACtC,QAAI,KAAK,mBAAmB,QAAQ,OAAO,IAAI,GAAG;AAChD,WAAK,mBAAmB,MAAM,EAAE,OAAO;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EACA,OAAO,SAAS;AACd,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK,UAAW,QAAO;AACpD,QAAI,CAAC,KAAK,mBAAoB,QAAO;AACrC,UAAM,QAAQ,KAAK,mBAAmB,QAAQ,OAAO;AACrD,QAAI,SAAS,GAAG;AACd,WAAK,mBAAmB,OAAO,OAAO,CAAC;AAAA,IACzC;AACA,WAAO;AAAA,EACT;AAAA,EACA,IAAIA,SAAQ,SAAS;AACnB,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK,UAAW,QAAO;AACpD,QAAI,CAAC,KAAK,gBAAiB,QAAO;AAClC,IAAAA,QAAO,MAAM,GAAG,EAAE,QAAQ,WAAS;AACjC,UAAI,OAAO,YAAY,aAAa;AAClC,aAAK,gBAAgB,KAAK,IAAI,CAAC;AAAA,MACjC,WAAW,KAAK,gBAAgB,KAAK,GAAG;AACtC,aAAK,gBAAgB,KAAK,EAAE,QAAQ,CAAC,cAAc,UAAU;AAC3D,cAAI,iBAAiB,WAAW,aAAa,kBAAkB,aAAa,mBAAmB,SAAS;AACtG,iBAAK,gBAAgB,KAAK,EAAE,OAAO,OAAO,CAAC;AAAA,UAC7C;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,OAAO;AACL,UAAM,OAAO;AACb,QAAI,CAAC,KAAK,mBAAmB,KAAK,UAAW,QAAO;AACpD,QAAI,CAAC,KAAK,gBAAiB,QAAO;AAClC,QAAIA;AACJ,QAAI;AACJ,QAAI;AACJ,aAAS,QAAQ,UAAU,QAAQ,OAAO,IAAI,MAAM,KAAK,GAAG,QAAQ,GAAG,QAAQ,OAAO,SAAS;AAC7F,WAAK,KAAK,IAAI,UAAU,KAAK;AAAA,IAC/B;AACA,QAAI,OAAO,KAAK,CAAC,MAAM,YAAY,MAAM,QAAQ,KAAK,CAAC,CAAC,GAAG;AACzD,MAAAA,UAAS,KAAK,CAAC;AACf,aAAO,KAAK,MAAM,GAAG,KAAK,MAAM;AAChC,gBAAU;AAAA,IACZ,OAAO;AACL,MAAAA,UAAS,KAAK,CAAC,EAAE;AACjB,aAAO,KAAK,CAAC,EAAE;AACf,gBAAU,KAAK,CAAC,EAAE,WAAW;AAAA,IAC/B;AACA,SAAK,QAAQ,OAAO;AACpB,UAAM,cAAc,MAAM,QAAQA,OAAM,IAAIA,UAASA,QAAO,MAAM,GAAG;AACrE,gBAAY,QAAQ,WAAS;AAC3B,UAAI,KAAK,sBAAsB,KAAK,mBAAmB,QAAQ;AAC7D,aAAK,mBAAmB,QAAQ,kBAAgB;AAC9C,uBAAa,MAAM,SAAS,CAAC,OAAO,GAAG,IAAI,CAAC;AAAA,QAC9C,CAAC;AAAA,MACH;AACA,UAAI,KAAK,mBAAmB,KAAK,gBAAgB,KAAK,GAAG;AACvD,aAAK,gBAAgB,KAAK,EAAE,QAAQ,kBAAgB;AAClD,uBAAa,MAAM,SAAS,IAAI;AAAA,QAClC,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,WAAO;AAAA,EACT;AACF;AACA,SAAS,aAAa;AACpB,QAAM,SAAS;AACf,MAAI;AACJ,MAAI;AACJ,QAAM,KAAK,OAAO;AAClB,MAAI,OAAO,OAAO,OAAO,UAAU,eAAe,OAAO,OAAO,UAAU,MAAM;AAC9E,YAAQ,OAAO,OAAO;AAAA,EACxB,OAAO;AACL,YAAQ,GAAG;AAAA,EACb;AACA,MAAI,OAAO,OAAO,OAAO,WAAW,eAAe,OAAO,OAAO,WAAW,MAAM;AAChF,aAAS,OAAO,OAAO;AAAA,EACzB,OAAO;AACL,aAAS,GAAG;AAAA,EACd;AACA,MAAI,UAAU,KAAK,OAAO,aAAa,KAAK,WAAW,KAAK,OAAO,WAAW,GAAG;AAC/E;AAAA,EACF;AAGA,UAAQ,QAAQ,SAAS,aAAa,IAAI,cAAc,KAAK,GAAG,EAAE,IAAI,SAAS,aAAa,IAAI,eAAe,KAAK,GAAG,EAAE;AACzH,WAAS,SAAS,SAAS,aAAa,IAAI,aAAa,KAAK,GAAG,EAAE,IAAI,SAAS,aAAa,IAAI,gBAAgB,KAAK,GAAG,EAAE;AAC3H,MAAI,OAAO,MAAM,KAAK,EAAG,SAAQ;AACjC,MAAI,OAAO,MAAM,MAAM,EAAG,UAAS;AACnC,SAAO,OAAO,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA,MAAM,OAAO,aAAa,IAAI,QAAQ;AAAA,EACxC,CAAC;AACH;AACA,SAAS,eAAe;AACtB,QAAM,SAAS;AACf,WAAS,0BAA0B,MAAM,OAAO;AAC9C,WAAO,WAAW,KAAK,iBAAiB,OAAO,kBAAkB,KAAK,CAAC,KAAK,CAAC;AAAA,EAC/E;AACA,QAAM,SAAS,OAAO;AACtB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,MAAM;AAAA,IACN,cAAc;AAAA,IACd;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,OAAO,WAAW,OAAO,QAAQ;AACnD,QAAM,uBAAuB,YAAY,OAAO,QAAQ,OAAO,SAAS,OAAO,OAAO;AACtF,QAAM,SAAS,gBAAgB,UAAU,IAAI,OAAO,OAAO,UAAU,gBAAgB;AACrF,QAAM,eAAe,YAAY,OAAO,QAAQ,OAAO,SAAS,OAAO;AACvE,MAAI,WAAW,CAAC;AAChB,QAAM,aAAa,CAAC;AACpB,QAAM,kBAAkB,CAAC;AACzB,MAAI,eAAe,OAAO;AAC1B,MAAI,OAAO,iBAAiB,YAAY;AACtC,mBAAe,OAAO,mBAAmB,KAAK,MAAM;AAAA,EACtD;AACA,MAAI,cAAc,OAAO;AACzB,MAAI,OAAO,gBAAgB,YAAY;AACrC,kBAAc,OAAO,kBAAkB,KAAK,MAAM;AAAA,EACpD;AACA,QAAM,yBAAyB,OAAO,SAAS;AAC/C,QAAM,2BAA2B,OAAO,WAAW;AACnD,MAAI,eAAe,OAAO;AAC1B,MAAI,gBAAgB,CAAC;AACrB,MAAI,gBAAgB;AACpB,MAAI,QAAQ;AACZ,MAAI,OAAO,eAAe,aAAa;AACrC;AAAA,EACF;AACA,MAAI,OAAO,iBAAiB,YAAY,aAAa,QAAQ,GAAG,KAAK,GAAG;AACtE,mBAAe,WAAW,aAAa,QAAQ,KAAK,EAAE,CAAC,IAAI,MAAM;AAAA,EACnE,WAAW,OAAO,iBAAiB,UAAU;AAC3C,mBAAe,WAAW,YAAY;AAAA,EACxC;AACA,SAAO,cAAc,CAAC;AAGtB,SAAO,QAAQ,aAAW;AACxB,QAAI,KAAK;AACP,cAAQ,MAAM,aAAa;AAAA,IAC7B,OAAO;AACL,cAAQ,MAAM,cAAc;AAAA,IAC9B;AACA,YAAQ,MAAM,eAAe;AAC7B,YAAQ,MAAM,YAAY;AAAA,EAC5B,CAAC;AAGD,MAAI,OAAO,kBAAkB,OAAO,SAAS;AAC3C,mBAAe,WAAW,mCAAmC,EAAE;AAC/D,mBAAe,WAAW,kCAAkC,EAAE;AAAA,EAChE;AACA,QAAM,cAAc,OAAO,QAAQ,OAAO,KAAK,OAAO,KAAK,OAAO;AAClE,MAAI,aAAa;AACf,WAAO,KAAK,WAAW,MAAM;AAAA,EAC/B,WAAW,OAAO,MAAM;AACtB,WAAO,KAAK,YAAY;AAAA,EAC1B;AAGA,MAAI;AACJ,QAAM,uBAAuB,OAAO,kBAAkB,UAAU,OAAO,eAAe,OAAO,KAAK,OAAO,WAAW,EAAE,OAAO,SAAO;AAClI,WAAO,OAAO,OAAO,YAAY,GAAG,EAAE,kBAAkB;AAAA,EAC1D,CAAC,EAAE,SAAS;AACZ,WAAS,IAAI,GAAG,IAAI,cAAc,KAAK,GAAG;AACxC,gBAAY;AACZ,QAAIC;AACJ,QAAI,OAAO,CAAC,EAAG,CAAAA,SAAQ,OAAO,CAAC;AAC/B,QAAI,aAAa;AACf,aAAO,KAAK,YAAY,GAAGA,QAAO,MAAM;AAAA,IAC1C;AACA,QAAI,OAAO,CAAC,KAAK,aAAaA,QAAO,SAAS,MAAM,OAAQ;AAE5D,QAAI,OAAO,kBAAkB,QAAQ;AACnC,UAAI,sBAAsB;AACxB,eAAO,CAAC,EAAE,MAAM,OAAO,kBAAkB,OAAO,CAAC,IAAI;AAAA,MACvD;AACA,YAAM,cAAc,iBAAiBA,MAAK;AAC1C,YAAM,mBAAmBA,OAAM,MAAM;AACrC,YAAM,yBAAyBA,OAAM,MAAM;AAC3C,UAAI,kBAAkB;AACpB,QAAAA,OAAM,MAAM,YAAY;AAAA,MAC1B;AACA,UAAI,wBAAwB;AAC1B,QAAAA,OAAM,MAAM,kBAAkB;AAAA,MAChC;AACA,UAAI,OAAO,cAAc;AACvB,oBAAY,OAAO,aAAa,IAAI,iBAAiBA,QAAO,SAAS,IAAI,IAAI,iBAAiBA,QAAO,UAAU,IAAI;AAAA,MACrH,OAAO;AAEL,cAAM,QAAQ,0BAA0B,aAAa,OAAO;AAC5D,cAAM,cAAc,0BAA0B,aAAa,cAAc;AACzE,cAAM,eAAe,0BAA0B,aAAa,eAAe;AAC3E,cAAM,aAAa,0BAA0B,aAAa,aAAa;AACvE,cAAM,cAAc,0BAA0B,aAAa,cAAc;AACzE,cAAM,YAAY,YAAY,iBAAiB,YAAY;AAC3D,YAAI,aAAa,cAAc,cAAc;AAC3C,sBAAY,QAAQ,aAAa;AAAA,QACnC,OAAO;AACL,gBAAM;AAAA,YACJ;AAAA,YACA;AAAA,UACF,IAAIA;AACJ,sBAAY,QAAQ,cAAc,eAAe,aAAa,eAAe,cAAc;AAAA,QAC7F;AAAA,MACF;AACA,UAAI,kBAAkB;AACpB,QAAAA,OAAM,MAAM,YAAY;AAAA,MAC1B;AACA,UAAI,wBAAwB;AAC1B,QAAAA,OAAM,MAAM,kBAAkB;AAAA,MAChC;AACA,UAAI,OAAO,aAAc,aAAY,KAAK,MAAM,SAAS;AAAA,IAC3D,OAAO;AACL,mBAAa,cAAc,OAAO,gBAAgB,KAAK,gBAAgB,OAAO;AAC9E,UAAI,OAAO,aAAc,aAAY,KAAK,MAAM,SAAS;AACzD,UAAI,OAAO,CAAC,GAAG;AACb,eAAO,CAAC,EAAE,MAAM,OAAO,kBAAkB,OAAO,CAAC,IAAI,GAAG,SAAS;AAAA,MACnE;AAAA,IACF;AACA,QAAI,OAAO,CAAC,GAAG;AACb,aAAO,CAAC,EAAE,kBAAkB;AAAA,IAC9B;AACA,oBAAgB,KAAK,SAAS;AAC9B,QAAI,OAAO,gBAAgB;AACzB,sBAAgB,gBAAgB,YAAY,IAAI,gBAAgB,IAAI;AACpE,UAAI,kBAAkB,KAAK,MAAM,EAAG,iBAAgB,gBAAgB,aAAa,IAAI;AACrF,UAAI,MAAM,EAAG,iBAAgB,gBAAgB,aAAa,IAAI;AAC9D,UAAI,KAAK,IAAI,aAAa,IAAI,IAAI,IAAM,iBAAgB;AACxD,UAAI,OAAO,aAAc,iBAAgB,KAAK,MAAM,aAAa;AACjE,UAAI,QAAQ,OAAO,mBAAmB,EAAG,UAAS,KAAK,aAAa;AACpE,iBAAW,KAAK,aAAa;AAAA,IAC/B,OAAO;AACL,UAAI,OAAO,aAAc,iBAAgB,KAAK,MAAM,aAAa;AACjE,WAAK,QAAQ,KAAK,IAAI,OAAO,OAAO,oBAAoB,KAAK,KAAK,OAAO,OAAO,mBAAmB,EAAG,UAAS,KAAK,aAAa;AACjI,iBAAW,KAAK,aAAa;AAC7B,sBAAgB,gBAAgB,YAAY;AAAA,IAC9C;AACA,WAAO,eAAe,YAAY;AAClC,oBAAgB;AAChB,aAAS;AAAA,EACX;AACA,SAAO,cAAc,KAAK,IAAI,OAAO,aAAa,UAAU,IAAI;AAChE,MAAI,OAAO,aAAa,OAAO,WAAW,WAAW,OAAO,WAAW,cAAc;AACnF,cAAU,MAAM,QAAQ,GAAG,OAAO,cAAc,YAAY;AAAA,EAC9D;AACA,MAAI,OAAO,gBAAgB;AACzB,cAAU,MAAM,OAAO,kBAAkB,OAAO,CAAC,IAAI,GAAG,OAAO,cAAc,YAAY;AAAA,EAC3F;AACA,MAAI,aAAa;AACf,WAAO,KAAK,kBAAkB,WAAW,QAAQ;AAAA,EACnD;AAGA,MAAI,CAAC,OAAO,gBAAgB;AAC1B,UAAM,gBAAgB,CAAC;AACvB,aAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK,GAAG;AAC3C,UAAI,iBAAiB,SAAS,CAAC;AAC/B,UAAI,OAAO,aAAc,kBAAiB,KAAK,MAAM,cAAc;AACnE,UAAI,SAAS,CAAC,KAAK,OAAO,cAAc,YAAY;AAClD,sBAAc,KAAK,cAAc;AAAA,MACnC;AAAA,IACF;AACA,eAAW;AACX,QAAI,KAAK,MAAM,OAAO,cAAc,UAAU,IAAI,KAAK,MAAM,SAAS,SAAS,SAAS,CAAC,CAAC,IAAI,GAAG;AAC/F,eAAS,KAAK,OAAO,cAAc,UAAU;AAAA,IAC/C;AAAA,EACF;AACA,MAAI,aAAa,OAAO,MAAM;AAC5B,UAAM,OAAO,gBAAgB,CAAC,IAAI;AAClC,QAAI,OAAO,iBAAiB,GAAG;AAC7B,YAAM,SAAS,KAAK,MAAM,OAAO,QAAQ,eAAe,OAAO,QAAQ,eAAe,OAAO,cAAc;AAC3G,YAAM,YAAY,OAAO,OAAO;AAChC,eAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,iBAAS,KAAK,SAAS,SAAS,SAAS,CAAC,IAAI,SAAS;AAAA,MACzD;AAAA,IACF;AACA,aAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,eAAe,OAAO,QAAQ,aAAa,KAAK,GAAG;AACpF,UAAI,OAAO,mBAAmB,GAAG;AAC/B,iBAAS,KAAK,SAAS,SAAS,SAAS,CAAC,IAAI,IAAI;AAAA,MACpD;AACA,iBAAW,KAAK,WAAW,WAAW,SAAS,CAAC,IAAI,IAAI;AACxD,aAAO,eAAe;AAAA,IACxB;AAAA,EACF;AACA,MAAI,SAAS,WAAW,EAAG,YAAW,CAAC,CAAC;AACxC,MAAI,iBAAiB,GAAG;AACtB,UAAM,MAAM,OAAO,aAAa,KAAK,MAAM,eAAe,OAAO,kBAAkB,aAAa;AAChG,WAAO,OAAO,CAAC,GAAG,eAAe;AAC/B,UAAI,CAAC,OAAO,WAAW,OAAO,KAAM,QAAO;AAC3C,UAAI,eAAe,OAAO,SAAS,GAAG;AACpC,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT,CAAC,EAAE,QAAQ,aAAW;AACpB,cAAQ,MAAM,GAAG,IAAI,GAAG,YAAY;AAAA,IACtC,CAAC;AAAA,EACH;AACA,MAAI,OAAO,kBAAkB,OAAO,sBAAsB;AACxD,QAAI,gBAAgB;AACpB,oBAAgB,QAAQ,oBAAkB;AACxC,uBAAiB,kBAAkB,gBAAgB;AAAA,IACrD,CAAC;AACD,qBAAiB;AACjB,UAAM,UAAU,gBAAgB,aAAa,gBAAgB,aAAa;AAC1E,eAAW,SAAS,IAAI,UAAQ;AAC9B,UAAI,QAAQ,EAAG,QAAO,CAAC;AACvB,UAAI,OAAO,QAAS,QAAO,UAAU;AACrC,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,MAAI,OAAO,0BAA0B;AACnC,QAAI,gBAAgB;AACpB,oBAAgB,QAAQ,oBAAkB;AACxC,uBAAiB,kBAAkB,gBAAgB;AAAA,IACrD,CAAC;AACD,qBAAiB;AACjB,UAAM,cAAc,OAAO,sBAAsB,MAAM,OAAO,qBAAqB;AACnF,QAAI,gBAAgB,aAAa,YAAY;AAC3C,YAAM,mBAAmB,aAAa,gBAAgB,cAAc;AACpE,eAAS,QAAQ,CAAC,MAAM,cAAc;AACpC,iBAAS,SAAS,IAAI,OAAO;AAAA,MAC/B,CAAC;AACD,iBAAW,QAAQ,CAAC,MAAM,cAAc;AACtC,mBAAW,SAAS,IAAI,OAAO;AAAA,MACjC,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO,OAAO,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,OAAO,kBAAkB,OAAO,WAAW,CAAC,OAAO,sBAAsB;AAC3E,mBAAe,WAAW,mCAAmC,GAAG,CAAC,SAAS,CAAC,CAAC,IAAI;AAChF,mBAAe,WAAW,kCAAkC,GAAG,OAAO,OAAO,IAAI,gBAAgB,gBAAgB,SAAS,CAAC,IAAI,CAAC,IAAI;AACpI,UAAM,gBAAgB,CAAC,OAAO,SAAS,CAAC;AACxC,UAAM,kBAAkB,CAAC,OAAO,WAAW,CAAC;AAC5C,WAAO,WAAW,OAAO,SAAS,IAAI,OAAK,IAAI,aAAa;AAC5D,WAAO,aAAa,OAAO,WAAW,IAAI,OAAK,IAAI,eAAe;AAAA,EACpE;AACA,MAAI,iBAAiB,sBAAsB;AACzC,WAAO,KAAK,oBAAoB;AAAA,EAClC;AACA,MAAI,SAAS,WAAW,wBAAwB;AAC9C,QAAI,OAAO,OAAO,cAAe,QAAO,cAAc;AACtD,WAAO,KAAK,sBAAsB;AAAA,EACpC;AACA,MAAI,WAAW,WAAW,0BAA0B;AAClD,WAAO,KAAK,wBAAwB;AAAA,EACtC;AACA,MAAI,OAAO,qBAAqB;AAC9B,WAAO,mBAAmB;AAAA,EAC5B;AACA,SAAO,KAAK,eAAe;AAC3B,MAAI,CAAC,aAAa,CAAC,OAAO,YAAY,OAAO,WAAW,WAAW,OAAO,WAAW,SAAS;AAC5F,UAAM,sBAAsB,GAAG,OAAO,sBAAsB;AAC5D,UAAM,6BAA6B,OAAO,GAAG,UAAU,SAAS,mBAAmB;AACnF,QAAI,gBAAgB,OAAO,yBAAyB;AAClD,UAAI,CAAC,2BAA4B,QAAO,GAAG,UAAU,IAAI,mBAAmB;AAAA,IAC9E,WAAW,4BAA4B;AACrC,aAAO,GAAG,UAAU,OAAO,mBAAmB;AAAA,IAChD;AAAA,EACF;AACF;AACA,SAAS,iBAAiB,OAAO;AAC/B,QAAM,SAAS;AACf,QAAM,eAAe,CAAC;AACtB,QAAM,YAAY,OAAO,WAAW,OAAO,OAAO,QAAQ;AAC1D,MAAI,YAAY;AAChB,MAAI;AACJ,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,cAAc,KAAK;AAAA,EAC5B,WAAW,UAAU,MAAM;AACzB,WAAO,cAAc,OAAO,OAAO,KAAK;AAAA,EAC1C;AACA,QAAM,kBAAkB,WAAS;AAC/B,QAAI,WAAW;AACb,aAAO,OAAO,OAAO,OAAO,oBAAoB,KAAK,CAAC;AAAA,IACxD;AACA,WAAO,OAAO,OAAO,KAAK;AAAA,EAC5B;AAEA,MAAI,OAAO,OAAO,kBAAkB,UAAU,OAAO,OAAO,gBAAgB,GAAG;AAC7E,QAAI,OAAO,OAAO,gBAAgB;AAChC,OAAC,OAAO,iBAAiB,CAAC,GAAG,QAAQ,CAAAA,WAAS;AAC5C,qBAAa,KAAKA,MAAK;AAAA,MACzB,CAAC;AAAA,IACH,OAAO;AACL,WAAK,IAAI,GAAG,IAAI,KAAK,KAAK,OAAO,OAAO,aAAa,GAAG,KAAK,GAAG;AAC9D,cAAM,QAAQ,OAAO,cAAc;AACnC,YAAI,QAAQ,OAAO,OAAO,UAAU,CAAC,UAAW;AAChD,qBAAa,KAAK,gBAAgB,KAAK,CAAC;AAAA,MAC1C;AAAA,IACF;AAAA,EACF,OAAO;AACL,iBAAa,KAAK,gBAAgB,OAAO,WAAW,CAAC;AAAA,EACvD;AAGA,OAAK,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK,GAAG;AAC3C,QAAI,OAAO,aAAa,CAAC,MAAM,aAAa;AAC1C,YAAM,SAAS,aAAa,CAAC,EAAE;AAC/B,kBAAY,SAAS,YAAY,SAAS;AAAA,IAC5C;AAAA,EACF;AAGA,MAAI,aAAa,cAAc,EAAG,QAAO,UAAU,MAAM,SAAS,GAAG,SAAS;AAChF;AACA,SAAS,qBAAqB;AAC5B,QAAM,SAAS;AACf,QAAM,SAAS,OAAO;AAEtB,QAAM,cAAc,OAAO,YAAY,OAAO,aAAa,IAAI,OAAO,UAAU,aAAa,OAAO,UAAU,YAAY;AAC1H,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,WAAO,CAAC,EAAE,qBAAqB,OAAO,aAAa,IAAI,OAAO,CAAC,EAAE,aAAa,OAAO,CAAC,EAAE,aAAa,cAAc,OAAO,sBAAsB;AAAA,EAClJ;AACF;AACA,IAAM,uBAAuB,CAAC,SAAS,WAAW,cAAc;AAC9D,MAAI,aAAa,CAAC,QAAQ,UAAU,SAAS,SAAS,GAAG;AACvD,YAAQ,UAAU,IAAI,SAAS;AAAA,EACjC,WAAW,CAAC,aAAa,QAAQ,UAAU,SAAS,SAAS,GAAG;AAC9D,YAAQ,UAAU,OAAO,SAAS;AAAA,EACpC;AACF;AACA,SAAS,qBAAqBC,YAAW;AACvC,MAAIA,eAAc,QAAQ;AACxB,IAAAA,aAAY,QAAQ,KAAK,aAAa;AAAA,EACxC;AACA,QAAM,SAAS;AACf,QAAM,SAAS,OAAO;AACtB,QAAM;AAAA,IACJ;AAAA,IACA,cAAc;AAAA,IACd;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,WAAW,EAAG;AACzB,MAAI,OAAO,OAAO,CAAC,EAAE,sBAAsB,YAAa,QAAO,mBAAmB;AAClF,MAAI,eAAe,CAACA;AACpB,MAAI,IAAK,gBAAeA;AACxB,SAAO,uBAAuB,CAAC;AAC/B,SAAO,gBAAgB,CAAC;AACxB,MAAI,eAAe,OAAO;AAC1B,MAAI,OAAO,iBAAiB,YAAY,aAAa,QAAQ,GAAG,KAAK,GAAG;AACtE,mBAAe,WAAW,aAAa,QAAQ,KAAK,EAAE,CAAC,IAAI,MAAM,OAAO;AAAA,EAC1E,WAAW,OAAO,iBAAiB,UAAU;AAC3C,mBAAe,WAAW,YAAY;AAAA,EACxC;AACA,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAMD,SAAQ,OAAO,CAAC;AACtB,QAAI,cAAcA,OAAM;AACxB,QAAI,OAAO,WAAW,OAAO,gBAAgB;AAC3C,qBAAe,OAAO,CAAC,EAAE;AAAA,IAC3B;AACA,UAAM,iBAAiB,gBAAgB,OAAO,iBAAiB,OAAO,aAAa,IAAI,KAAK,gBAAgBA,OAAM,kBAAkB;AACpI,UAAM,yBAAyB,eAAe,SAAS,CAAC,KAAK,OAAO,iBAAiB,OAAO,aAAa,IAAI,KAAK,gBAAgBA,OAAM,kBAAkB;AAC1J,UAAM,cAAc,EAAE,eAAe;AACrC,UAAM,aAAa,cAAc,OAAO,gBAAgB,CAAC;AACzD,UAAM,iBAAiB,eAAe,KAAK,eAAe,OAAO,OAAO,OAAO,gBAAgB,CAAC;AAChG,UAAM,YAAY,eAAe,KAAK,cAAc,OAAO,OAAO,KAAK,aAAa,KAAK,cAAc,OAAO,QAAQ,eAAe,KAAK,cAAc,OAAO;AAC/J,QAAI,WAAW;AACb,aAAO,cAAc,KAAKA,MAAK;AAC/B,aAAO,qBAAqB,KAAK,CAAC;AAAA,IACpC;AACA,yBAAqBA,QAAO,WAAW,OAAO,iBAAiB;AAC/D,yBAAqBA,QAAO,gBAAgB,OAAO,sBAAsB;AACzE,IAAAA,OAAM,WAAW,MAAM,CAAC,gBAAgB;AACxC,IAAAA,OAAM,mBAAmB,MAAM,CAAC,wBAAwB;AAAA,EAC1D;AACF;AACA,SAAS,eAAeC,YAAW;AACjC,QAAM,SAAS;AACf,MAAI,OAAOA,eAAc,aAAa;AACpC,UAAM,aAAa,OAAO,eAAe,KAAK;AAE9C,IAAAA,aAAY,UAAU,OAAO,aAAa,OAAO,YAAY,cAAc;AAAA,EAC7E;AACA,QAAM,SAAS,OAAO;AACtB,QAAM,iBAAiB,OAAO,aAAa,IAAI,OAAO,aAAa;AACnE,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,eAAe;AACrB,QAAM,SAAS;AACf,MAAI,mBAAmB,GAAG;AACxB,eAAW;AACX,kBAAc;AACd,YAAQ;AAAA,EACV,OAAO;AACL,gBAAYA,aAAY,OAAO,aAAa,KAAK;AACjD,UAAM,qBAAqB,KAAK,IAAIA,aAAY,OAAO,aAAa,CAAC,IAAI;AACzE,UAAM,eAAe,KAAK,IAAIA,aAAY,OAAO,aAAa,CAAC,IAAI;AACnE,kBAAc,sBAAsB,YAAY;AAChD,YAAQ,gBAAgB,YAAY;AACpC,QAAI,mBAAoB,YAAW;AACnC,QAAI,aAAc,YAAW;AAAA,EAC/B;AACA,MAAI,OAAO,MAAM;AACf,UAAM,kBAAkB,OAAO,oBAAoB,CAAC;AACpD,UAAM,iBAAiB,OAAO,oBAAoB,OAAO,OAAO,SAAS,CAAC;AAC1E,UAAM,sBAAsB,OAAO,WAAW,eAAe;AAC7D,UAAM,qBAAqB,OAAO,WAAW,cAAc;AAC3D,UAAM,eAAe,OAAO,WAAW,OAAO,WAAW,SAAS,CAAC;AACnE,UAAM,eAAe,KAAK,IAAIA,UAAS;AACvC,QAAI,gBAAgB,qBAAqB;AACvC,sBAAgB,eAAe,uBAAuB;AAAA,IACxD,OAAO;AACL,sBAAgB,eAAe,eAAe,sBAAsB;AAAA,IACtE;AACA,QAAI,eAAe,EAAG,iBAAgB;AAAA,EACxC;AACA,SAAO,OAAO,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,OAAO,uBAAuB,OAAO,kBAAkB,OAAO,WAAY,QAAO,qBAAqBA,UAAS;AACnH,MAAI,eAAe,CAAC,cAAc;AAChC,WAAO,KAAK,uBAAuB;AAAA,EACrC;AACA,MAAI,SAAS,CAAC,QAAQ;AACpB,WAAO,KAAK,iBAAiB;AAAA,EAC/B;AACA,MAAI,gBAAgB,CAAC,eAAe,UAAU,CAAC,OAAO;AACpD,WAAO,KAAK,UAAU;AAAA,EACxB;AACA,SAAO,KAAK,YAAY,QAAQ;AAClC;AACA,IAAM,qBAAqB,CAAC,SAAS,WAAW,cAAc;AAC5D,MAAI,aAAa,CAAC,QAAQ,UAAU,SAAS,SAAS,GAAG;AACvD,YAAQ,UAAU,IAAI,SAAS;AAAA,EACjC,WAAW,CAAC,aAAa,QAAQ,UAAU,SAAS,SAAS,GAAG;AAC9D,YAAQ,UAAU,OAAO,SAAS;AAAA,EACpC;AACF;AACA,SAAS,sBAAsB;AAC7B,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,OAAO,WAAW,OAAO,QAAQ;AACnD,QAAM,cAAc,OAAO,QAAQ,OAAO,QAAQ,OAAO,KAAK,OAAO;AACrE,QAAM,mBAAmB,cAAY;AACnC,WAAO,gBAAgB,UAAU,IAAI,OAAO,UAAU,GAAG,QAAQ,iBAAiB,QAAQ,EAAE,EAAE,CAAC;AAAA,EACjG;AACA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI,WAAW;AACb,QAAI,OAAO,MAAM;AACf,UAAI,aAAa,cAAc,OAAO,QAAQ;AAC9C,UAAI,aAAa,EAAG,cAAa,OAAO,QAAQ,OAAO,SAAS;AAChE,UAAI,cAAc,OAAO,QAAQ,OAAO,OAAQ,eAAc,OAAO,QAAQ,OAAO;AACpF,oBAAc,iBAAiB,6BAA6B,UAAU,IAAI;AAAA,IAC5E,OAAO;AACL,oBAAc,iBAAiB,6BAA6B,WAAW,IAAI;AAAA,IAC7E;AAAA,EACF,OAAO;AACL,QAAI,aAAa;AACf,oBAAc,OAAO,KAAK,aAAW,QAAQ,WAAW,WAAW;AACnE,kBAAY,OAAO,KAAK,aAAW,QAAQ,WAAW,cAAc,CAAC;AACrE,kBAAY,OAAO,KAAK,aAAW,QAAQ,WAAW,cAAc,CAAC;AAAA,IACvE,OAAO;AACL,oBAAc,OAAO,WAAW;AAAA,IAClC;AAAA,EACF;AACA,MAAI,aAAa;AACf,QAAI,CAAC,aAAa;AAEhB,kBAAY,eAAe,aAAa,IAAI,OAAO,UAAU,gBAAgB,EAAE,CAAC;AAChF,UAAI,OAAO,QAAQ,CAAC,WAAW;AAC7B,oBAAY,OAAO,CAAC;AAAA,MACtB;AAGA,kBAAY,eAAe,aAAa,IAAI,OAAO,UAAU,gBAAgB,EAAE,CAAC;AAChF,UAAI,OAAO,QAAQ,CAAC,cAAc,GAAG;AACnC,oBAAY,OAAO,OAAO,SAAS,CAAC;AAAA,MACtC;AAAA,IACF;AAAA,EACF;AACA,SAAO,QAAQ,aAAW;AACxB,uBAAmB,SAAS,YAAY,aAAa,OAAO,gBAAgB;AAC5E,uBAAmB,SAAS,YAAY,WAAW,OAAO,cAAc;AACxE,uBAAmB,SAAS,YAAY,WAAW,OAAO,cAAc;AAAA,EAC1E,CAAC;AACD,SAAO,kBAAkB;AAC3B;AACA,IAAM,uBAAuB,CAAC,QAAQ,YAAY;AAChD,MAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO,OAAQ;AACnD,QAAM,gBAAgB,MAAM,OAAO,YAAY,iBAAiB,IAAI,OAAO,OAAO,UAAU;AAC5F,QAAM,UAAU,QAAQ,QAAQ,cAAc,CAAC;AAC/C,MAAI,SAAS;AACX,QAAI,SAAS,QAAQ,cAAc,IAAI,OAAO,OAAO,kBAAkB,EAAE;AACzE,QAAI,CAAC,UAAU,OAAO,WAAW;AAC/B,UAAI,QAAQ,YAAY;AACtB,iBAAS,QAAQ,WAAW,cAAc,IAAI,OAAO,OAAO,kBAAkB,EAAE;AAAA,MAClF,OAAO;AAEL,8BAAsB,MAAM;AAC1B,cAAI,QAAQ,YAAY;AACtB,qBAAS,QAAQ,WAAW,cAAc,IAAI,OAAO,OAAO,kBAAkB,EAAE;AAChF,gBAAI,OAAQ,QAAO,OAAO;AAAA,UAC5B;AAAA,QACF,CAAC;AAAA,MACH;AAAA,IACF;AACA,QAAI,OAAQ,QAAO,OAAO;AAAA,EAC5B;AACF;AACA,IAAM,SAAS,CAAC,QAAQ,UAAU;AAChC,MAAI,CAAC,OAAO,OAAO,KAAK,EAAG;AAC3B,QAAM,UAAU,OAAO,OAAO,KAAK,EAAE,cAAc,kBAAkB;AACrE,MAAI,QAAS,SAAQ,gBAAgB,SAAS;AAChD;AACA,IAAM,UAAU,YAAU;AACxB,MAAI,CAAC,UAAU,OAAO,aAAa,CAAC,OAAO,OAAQ;AACnD,MAAI,SAAS,OAAO,OAAO;AAC3B,QAAM,MAAM,OAAO,OAAO;AAC1B,MAAI,CAAC,OAAO,CAAC,UAAU,SAAS,EAAG;AACnC,WAAS,KAAK,IAAI,QAAQ,GAAG;AAC7B,QAAM,gBAAgB,OAAO,OAAO,kBAAkB,SAAS,OAAO,qBAAqB,IAAI,KAAK,KAAK,OAAO,OAAO,aAAa;AACpI,QAAM,cAAc,OAAO;AAC3B,MAAI,OAAO,OAAO,QAAQ,OAAO,OAAO,KAAK,OAAO,GAAG;AACrD,UAAM,eAAe;AACrB,UAAM,iBAAiB,CAAC,eAAe,MAAM;AAC7C,mBAAe,KAAK,GAAG,MAAM,KAAK;AAAA,MAChC,QAAQ;AAAA,IACV,CAAC,EAAE,IAAI,CAAC,GAAG,MAAM;AACf,aAAO,eAAe,gBAAgB;AAAA,IACxC,CAAC,CAAC;AACF,WAAO,OAAO,QAAQ,CAAC,SAAS,MAAM;AACpC,UAAI,eAAe,SAAS,QAAQ,MAAM,EAAG,QAAO,QAAQ,CAAC;AAAA,IAC/D,CAAC;AACD;AAAA,EACF;AACA,QAAM,uBAAuB,cAAc,gBAAgB;AAC3D,MAAI,OAAO,OAAO,UAAU,OAAO,OAAO,MAAM;AAC9C,aAAS,IAAI,cAAc,QAAQ,KAAK,uBAAuB,QAAQ,KAAK,GAAG;AAC7E,YAAM,aAAa,IAAI,MAAM,OAAO;AACpC,UAAI,YAAY,eAAe,YAAY,qBAAsB,QAAO,QAAQ,SAAS;AAAA,IAC3F;AAAA,EACF,OAAO;AACL,aAAS,IAAI,KAAK,IAAI,cAAc,QAAQ,CAAC,GAAG,KAAK,KAAK,IAAI,uBAAuB,QAAQ,MAAM,CAAC,GAAG,KAAK,GAAG;AAC7G,UAAI,MAAM,gBAAgB,IAAI,wBAAwB,IAAI,cAAc;AACtE,eAAO,QAAQ,CAAC;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACF;AACA,SAAS,0BAA0B,QAAQ;AACzC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAMA,aAAY,OAAO,eAAe,OAAO,YAAY,CAAC,OAAO;AACnE,MAAI;AACJ,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC7C,QAAI,OAAO,WAAW,IAAI,CAAC,MAAM,aAAa;AAC5C,UAAIA,cAAa,WAAW,CAAC,KAAKA,aAAY,WAAW,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC,IAAI,WAAW,CAAC,KAAK,GAAG;AACzG,sBAAc;AAAA,MAChB,WAAWA,cAAa,WAAW,CAAC,KAAKA,aAAY,WAAW,IAAI,CAAC,GAAG;AACtE,sBAAc,IAAI;AAAA,MACpB;AAAA,IACF,WAAWA,cAAa,WAAW,CAAC,GAAG;AACrC,oBAAc;AAAA,IAChB;AAAA,EACF;AAEA,MAAI,OAAO,qBAAqB;AAC9B,QAAI,cAAc,KAAK,OAAO,gBAAgB,YAAa,eAAc;AAAA,EAC3E;AACA,SAAO;AACT;AACA,SAAS,kBAAkB,gBAAgB;AACzC,QAAM,SAAS;AACf,QAAMA,aAAY,OAAO,eAAe,OAAO,YAAY,CAAC,OAAO;AACnE,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,aAAa;AAAA,IACb,WAAW;AAAA,IACX,WAAW;AAAA,EACb,IAAI;AACJ,MAAI,cAAc;AAClB,MAAI;AACJ,QAAM,sBAAsB,YAAU;AACpC,QAAIC,aAAY,SAAS,OAAO,QAAQ;AACxC,QAAIA,aAAY,GAAG;AACjB,MAAAA,aAAY,OAAO,QAAQ,OAAO,SAASA;AAAA,IAC7C;AACA,QAAIA,cAAa,OAAO,QAAQ,OAAO,QAAQ;AAC7C,MAAAA,cAAa,OAAO,QAAQ,OAAO;AAAA,IACrC;AACA,WAAOA;AAAA,EACT;AACA,MAAI,OAAO,gBAAgB,aAAa;AACtC,kBAAc,0BAA0B,MAAM;AAAA,EAChD;AACA,MAAI,SAAS,QAAQD,UAAS,KAAK,GAAG;AACpC,gBAAY,SAAS,QAAQA,UAAS;AAAA,EACxC,OAAO;AACL,UAAM,OAAO,KAAK,IAAI,OAAO,oBAAoB,WAAW;AAC5D,gBAAY,OAAO,KAAK,OAAO,cAAc,QAAQ,OAAO,cAAc;AAAA,EAC5E;AACA,MAAI,aAAa,SAAS,OAAQ,aAAY,SAAS,SAAS;AAChE,MAAI,gBAAgB,iBAAiB,CAAC,OAAO,OAAO,MAAM;AACxD,QAAI,cAAc,mBAAmB;AACnC,aAAO,YAAY;AACnB,aAAO,KAAK,iBAAiB;AAAA,IAC/B;AACA;AAAA,EACF;AACA,MAAI,gBAAgB,iBAAiB,OAAO,OAAO,QAAQ,OAAO,WAAW,OAAO,OAAO,QAAQ,SAAS;AAC1G,WAAO,YAAY,oBAAoB,WAAW;AAClD;AAAA,EACF;AACA,QAAM,cAAc,OAAO,QAAQ,OAAO,QAAQ,OAAO,KAAK,OAAO;AAGrE,MAAI;AACJ,MAAI,OAAO,WAAW,OAAO,QAAQ,WAAW,OAAO,MAAM;AAC3D,gBAAY,oBAAoB,WAAW;AAAA,EAC7C,WAAW,aAAa;AACtB,UAAM,qBAAqB,OAAO,OAAO,KAAK,aAAW,QAAQ,WAAW,WAAW;AACvF,QAAI,mBAAmB,SAAS,mBAAmB,aAAa,yBAAyB,GAAG,EAAE;AAC9F,QAAI,OAAO,MAAM,gBAAgB,GAAG;AAClC,yBAAmB,KAAK,IAAI,OAAO,OAAO,QAAQ,kBAAkB,GAAG,CAAC;AAAA,IAC1E;AACA,gBAAY,KAAK,MAAM,mBAAmB,OAAO,KAAK,IAAI;AAAA,EAC5D,WAAW,OAAO,OAAO,WAAW,GAAG;AACrC,UAAM,aAAa,OAAO,OAAO,WAAW,EAAE,aAAa,yBAAyB;AACpF,QAAI,YAAY;AACd,kBAAY,SAAS,YAAY,EAAE;AAAA,IACrC,OAAO;AACL,kBAAY;AAAA,IACd;AAAA,EACF,OAAO;AACL,gBAAY;AAAA,EACd;AACA,SAAO,OAAO,QAAQ;AAAA,IACpB;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,CAAC;AACD,MAAI,OAAO,aAAa;AACtB,YAAQ,MAAM;AAAA,EAChB;AACA,SAAO,KAAK,mBAAmB;AAC/B,SAAO,KAAK,iBAAiB;AAC7B,MAAI,OAAO,eAAe,OAAO,OAAO,oBAAoB;AAC1D,QAAI,sBAAsB,WAAW;AACnC,aAAO,KAAK,iBAAiB;AAAA,IAC/B;AACA,WAAO,KAAK,aAAa;AAAA,EAC3B;AACF;AACA,SAAS,mBAAmB,IAAI,MAAM;AACpC,QAAM,SAAS;AACf,QAAM,SAAS,OAAO;AACtB,MAAID,SAAQ,GAAG,QAAQ,IAAI,OAAO,UAAU,gBAAgB;AAC5D,MAAI,CAACA,UAAS,OAAO,aAAa,QAAQ,KAAK,SAAS,KAAK,KAAK,SAAS,EAAE,GAAG;AAC9E,KAAC,GAAG,KAAK,MAAM,KAAK,QAAQ,EAAE,IAAI,GAAG,KAAK,MAAM,CAAC,EAAE,QAAQ,YAAU;AACnE,UAAI,CAACA,UAAS,OAAO,WAAW,OAAO,QAAQ,IAAI,OAAO,UAAU,gBAAgB,GAAG;AACrF,QAAAA,SAAQ;AAAA,MACV;AAAA,IACF,CAAC;AAAA,EACH;AACA,MAAI,aAAa;AACjB,MAAI;AACJ,MAAIA,QAAO;AACT,aAAS,IAAI,GAAG,IAAI,OAAO,OAAO,QAAQ,KAAK,GAAG;AAChD,UAAI,OAAO,OAAO,CAAC,MAAMA,QAAO;AAC9B,qBAAa;AACb,qBAAa;AACb;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,MAAIA,UAAS,YAAY;AACvB,WAAO,eAAeA;AACtB,QAAI,OAAO,WAAW,OAAO,OAAO,QAAQ,SAAS;AACnD,aAAO,eAAe,SAASA,OAAM,aAAa,yBAAyB,GAAG,EAAE;AAAA,IAClF,OAAO;AACL,aAAO,eAAe;AAAA,IACxB;AAAA,EACF,OAAO;AACL,WAAO,eAAe;AACtB,WAAO,eAAe;AACtB;AAAA,EACF;AACA,MAAI,OAAO,uBAAuB,OAAO,iBAAiB,UAAa,OAAO,iBAAiB,OAAO,aAAa;AACjH,WAAO,oBAAoB;AAAA,EAC7B;AACF;AACA,IAAI,SAAS;AAAA,EACX;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,mBAAmB,MAAM;AAChC,MAAI,SAAS,QAAQ;AACnB,WAAO,KAAK,aAAa,IAAI,MAAM;AAAA,EACrC;AACA,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA,cAAc;AAAA,IACd,WAAAC;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,kBAAkB;AAC3B,WAAO,MAAM,CAACA,aAAYA;AAAA,EAC5B;AACA,MAAI,OAAO,SAAS;AAClB,WAAOA;AAAA,EACT;AACA,MAAI,mBAAmB,aAAa,WAAW,IAAI;AACnD,sBAAoB,OAAO,sBAAsB;AACjD,MAAI,IAAK,oBAAmB,CAAC;AAC7B,SAAO,oBAAoB;AAC7B;AACA,SAAS,aAAaA,YAAW,cAAc;AAC7C,QAAM,SAAS;AACf,QAAM;AAAA,IACJ,cAAc;AAAA,IACd;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,IAAI;AACR,MAAI,IAAI;AACR,QAAM,IAAI;AACV,MAAI,OAAO,aAAa,GAAG;AACzB,QAAI,MAAM,CAACA,aAAYA;AAAA,EACzB,OAAO;AACL,QAAIA;AAAA,EACN;AACA,MAAI,OAAO,cAAc;AACvB,QAAI,KAAK,MAAM,CAAC;AAChB,QAAI,KAAK,MAAM,CAAC;AAAA,EAClB;AACA,SAAO,oBAAoB,OAAO;AAClC,SAAO,YAAY,OAAO,aAAa,IAAI,IAAI;AAC/C,MAAI,OAAO,SAAS;AAClB,cAAU,OAAO,aAAa,IAAI,eAAe,WAAW,IAAI,OAAO,aAAa,IAAI,CAAC,IAAI,CAAC;AAAA,EAChG,WAAW,CAAC,OAAO,kBAAkB;AACnC,QAAI,OAAO,aAAa,GAAG;AACzB,WAAK,OAAO,sBAAsB;AAAA,IACpC,OAAO;AACL,WAAK,OAAO,sBAAsB;AAAA,IACpC;AACA,cAAU,MAAM,YAAY,eAAe,CAAC,OAAO,CAAC,OAAO,CAAC;AAAA,EAC9D;AAGA,MAAI;AACJ,QAAM,iBAAiB,OAAO,aAAa,IAAI,OAAO,aAAa;AACnE,MAAI,mBAAmB,GAAG;AACxB,kBAAc;AAAA,EAChB,OAAO;AACL,mBAAeA,aAAY,OAAO,aAAa,KAAK;AAAA,EACtD;AACA,MAAI,gBAAgB,UAAU;AAC5B,WAAO,eAAeA,UAAS;AAAA,EACjC;AACA,SAAO,KAAK,gBAAgB,OAAO,WAAW,YAAY;AAC5D;AACA,SAAS,eAAe;AACtB,SAAO,CAAC,KAAK,SAAS,CAAC;AACzB;AACA,SAAS,eAAe;AACtB,SAAO,CAAC,KAAK,SAAS,KAAK,SAAS,SAAS,CAAC;AAChD;AACA,SAAS,YAAYA,YAAW,OAAO,cAAc,iBAAiB,UAAU;AAC9E,MAAIA,eAAc,QAAQ;AACxB,IAAAA,aAAY;AAAA,EACd;AACA,MAAI,UAAU,QAAQ;AACpB,YAAQ,KAAK,OAAO;AAAA,EACtB;AACA,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,oBAAoB,QAAQ;AAC9B,sBAAkB;AAAA,EACpB;AACA,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,aAAa,OAAO,gCAAgC;AAC7D,WAAO;AAAA,EACT;AACA,QAAME,gBAAe,OAAO,aAAa;AACzC,QAAMC,gBAAe,OAAO,aAAa;AACzC,MAAI;AACJ,MAAI,mBAAmBH,aAAYE,cAAc,gBAAeA;AAAA,WAAsB,mBAAmBF,aAAYG,cAAc,gBAAeA;AAAA,MAAkB,gBAAeH;AAGnL,SAAO,eAAe,YAAY;AAClC,MAAI,OAAO,SAAS;AAClB,UAAM,MAAM,OAAO,aAAa;AAChC,QAAI,UAAU,GAAG;AACf,gBAAU,MAAM,eAAe,WAAW,IAAI,CAAC;AAAA,IACjD,OAAO;AACL,UAAI,CAAC,OAAO,QAAQ,cAAc;AAChC,6BAAqB;AAAA,UACnB;AAAA,UACA,gBAAgB,CAAC;AAAA,UACjB,MAAM,MAAM,SAAS;AAAA,QACvB,CAAC;AACD,eAAO;AAAA,MACT;AACA,gBAAU,SAAS;AAAA,QACjB,CAAC,MAAM,SAAS,KAAK,GAAG,CAAC;AAAA,QACzB,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,MAAI,UAAU,GAAG;AACf,WAAO,cAAc,CAAC;AACtB,WAAO,aAAa,YAAY;AAChC,QAAI,cAAc;AAChB,aAAO,KAAK,yBAAyB,OAAO,QAAQ;AACpD,aAAO,KAAK,eAAe;AAAA,IAC7B;AAAA,EACF,OAAO;AACL,WAAO,cAAc,KAAK;AAC1B,WAAO,aAAa,YAAY;AAChC,QAAI,cAAc;AAChB,aAAO,KAAK,yBAAyB,OAAO,QAAQ;AACpD,aAAO,KAAK,iBAAiB;AAAA,IAC/B;AACA,QAAI,CAAC,OAAO,WAAW;AACrB,aAAO,YAAY;AACnB,UAAI,CAAC,OAAO,mCAAmC;AAC7C,eAAO,oCAAoC,SAASI,eAAc,GAAG;AACnE,cAAI,CAAC,UAAU,OAAO,UAAW;AACjC,cAAI,EAAE,WAAW,KAAM;AACvB,iBAAO,UAAU,oBAAoB,iBAAiB,OAAO,iCAAiC;AAC9F,iBAAO,oCAAoC;AAC3C,iBAAO,OAAO;AACd,iBAAO,YAAY;AACnB,cAAI,cAAc;AAChB,mBAAO,KAAK,eAAe;AAAA,UAC7B;AAAA,QACF;AAAA,MACF;AACA,aAAO,UAAU,iBAAiB,iBAAiB,OAAO,iCAAiC;AAAA,IAC7F;AAAA,EACF;AACA,SAAO;AACT;AACA,IAAI,YAAY;AAAA,EACd,cAAc;AAAA,EACd;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,cAAc,UAAU,cAAc;AAC7C,QAAM,SAAS;AACf,MAAI,CAAC,OAAO,OAAO,SAAS;AAC1B,WAAO,UAAU,MAAM,qBAAqB,GAAG,QAAQ;AACvD,WAAO,UAAU,MAAM,kBAAkB,aAAa,IAAI,QAAQ;AAAA,EACpE;AACA,SAAO,KAAK,iBAAiB,UAAU,YAAY;AACrD;AACA,SAAS,eAAe,MAAM;AAC5B,MAAI;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,MAAM;AACV,MAAI,CAAC,KAAK;AACR,QAAI,cAAc,cAAe,OAAM;AAAA,aAAgB,cAAc,cAAe,OAAM;AAAA,QAAY,OAAM;AAAA,EAC9G;AACA,SAAO,KAAK,aAAa,IAAI,EAAE;AAC/B,MAAI,gBAAgB,QAAQ,SAAS;AACnC,WAAO,KAAK,uBAAuB,IAAI,EAAE;AAAA,EAC3C,WAAW,gBAAgB,gBAAgB,eAAe;AACxD,WAAO,KAAK,wBAAwB,IAAI,EAAE;AAC1C,QAAI,QAAQ,QAAQ;AAClB,aAAO,KAAK,sBAAsB,IAAI,EAAE;AAAA,IAC1C,OAAO;AACL,aAAO,KAAK,sBAAsB,IAAI,EAAE;AAAA,IAC1C;AAAA,EACF;AACF;AACA,SAAS,gBAAgB,cAAc,WAAW;AAChD,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,OAAO,QAAS;AACpB,MAAI,OAAO,YAAY;AACrB,WAAO,iBAAiB;AAAA,EAC1B;AACA,iBAAe;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR,CAAC;AACH;AACA,SAAS,cAAc,cAAc,WAAW;AAC9C,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,YAAY;AACnB,MAAI,OAAO,QAAS;AACpB,SAAO,cAAc,CAAC;AACtB,iBAAe;AAAA,IACb;AAAA,IACA;AAAA,IACA;AAAA,IACA,MAAM;AAAA,EACR,CAAC;AACH;AACA,IAAI,aAAa;AAAA,EACf;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,QAAQ,OAAO,OAAO,cAAc,UAAU,SAAS;AAC9D,MAAI,UAAU,QAAQ;AACpB,YAAQ;AAAA,EACV;AACA,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,YAAQ,SAAS,OAAO,EAAE;AAAA,EAC5B;AACA,QAAM,SAAS;AACf,MAAI,aAAa;AACjB,MAAI,aAAa,EAAG,cAAa;AACjC,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,WAAW,CAAC,YAAY,CAAC,WAAW,OAAO,aAAa,OAAO,aAAa,OAAO,gCAAgC;AACtH,WAAO;AAAA,EACT;AACA,MAAI,OAAO,UAAU,aAAa;AAChC,YAAQ,OAAO,OAAO;AAAA,EACxB;AACA,QAAM,OAAO,KAAK,IAAI,OAAO,OAAO,oBAAoB,UAAU;AAClE,MAAI,YAAY,OAAO,KAAK,OAAO,aAAa,QAAQ,OAAO,OAAO,cAAc;AACpF,MAAI,aAAa,SAAS,OAAQ,aAAY,SAAS,SAAS;AAChE,QAAMJ,aAAY,CAAC,SAAS,SAAS;AAErC,MAAI,OAAO,qBAAqB;AAC9B,aAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,GAAG;AAC7C,YAAM,sBAAsB,CAAC,KAAK,MAAMA,aAAY,GAAG;AACvD,YAAM,iBAAiB,KAAK,MAAM,WAAW,CAAC,IAAI,GAAG;AACrD,YAAM,qBAAqB,KAAK,MAAM,WAAW,IAAI,CAAC,IAAI,GAAG;AAC7D,UAAI,OAAO,WAAW,IAAI,CAAC,MAAM,aAAa;AAC5C,YAAI,uBAAuB,kBAAkB,sBAAsB,sBAAsB,qBAAqB,kBAAkB,GAAG;AACjI,uBAAa;AAAA,QACf,WAAW,uBAAuB,kBAAkB,sBAAsB,oBAAoB;AAC5F,uBAAa,IAAI;AAAA,QACnB;AAAA,MACF,WAAW,uBAAuB,gBAAgB;AAChD,qBAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF;AAEA,MAAI,OAAO,eAAe,eAAe,aAAa;AACpD,QAAI,CAAC,OAAO,mBAAmB,MAAMA,aAAY,OAAO,aAAaA,aAAY,OAAO,aAAa,IAAIA,aAAY,OAAO,aAAaA,aAAY,OAAO,aAAa,IAAI;AAC3K,aAAO;AAAA,IACT;AACA,QAAI,CAAC,OAAO,kBAAkBA,aAAY,OAAO,aAAaA,aAAY,OAAO,aAAa,GAAG;AAC/F,WAAK,eAAe,OAAO,YAAY;AACrC,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,MAAI,gBAAgB,iBAAiB,MAAM,cAAc;AACvD,WAAO,KAAK,wBAAwB;AAAA,EACtC;AAGA,SAAO,eAAeA,UAAS;AAC/B,MAAI;AACJ,MAAI,aAAa,YAAa,aAAY;AAAA,WAAgB,aAAa,YAAa,aAAY;AAAA,MAAY,aAAY;AAGxH,QAAM,YAAY,OAAO,WAAW,OAAO,OAAO,QAAQ;AAC1D,QAAM,mBAAmB,aAAa;AAEtC,MAAI,CAAC,qBAAqB,OAAO,CAACA,eAAc,OAAO,aAAa,CAAC,OAAOA,eAAc,OAAO,YAAY;AAC3G,WAAO,kBAAkB,UAAU;AAEnC,QAAI,OAAO,YAAY;AACrB,aAAO,iBAAiB;AAAA,IAC1B;AACA,WAAO,oBAAoB;AAC3B,QAAI,OAAO,WAAW,SAAS;AAC7B,aAAO,aAAaA,UAAS;AAAA,IAC/B;AACA,QAAI,cAAc,SAAS;AACzB,aAAO,gBAAgB,cAAc,SAAS;AAC9C,aAAO,cAAc,cAAc,SAAS;AAAA,IAC9C;AACA,WAAO;AAAA,EACT;AACA,MAAI,OAAO,SAAS;AAClB,UAAM,MAAM,OAAO,aAAa;AAChC,UAAM,IAAI,MAAMA,aAAY,CAACA;AAC7B,QAAI,UAAU,GAAG;AACf,UAAI,WAAW;AACb,eAAO,UAAU,MAAM,iBAAiB;AACxC,eAAO,oBAAoB;AAAA,MAC7B;AACA,UAAI,aAAa,CAAC,OAAO,6BAA6B,OAAO,OAAO,eAAe,GAAG;AACpF,eAAO,4BAA4B;AACnC,8BAAsB,MAAM;AAC1B,oBAAU,MAAM,eAAe,WAAW,IAAI;AAAA,QAChD,CAAC;AAAA,MACH,OAAO;AACL,kBAAU,MAAM,eAAe,WAAW,IAAI;AAAA,MAChD;AACA,UAAI,WAAW;AACb,8BAAsB,MAAM;AAC1B,iBAAO,UAAU,MAAM,iBAAiB;AACxC,iBAAO,oBAAoB;AAAA,QAC7B,CAAC;AAAA,MACH;AAAA,IACF,OAAO;AACL,UAAI,CAAC,OAAO,QAAQ,cAAc;AAChC,6BAAqB;AAAA,UACnB;AAAA,UACA,gBAAgB;AAAA,UAChB,MAAM,MAAM,SAAS;AAAA,QACvB,CAAC;AACD,eAAO;AAAA,MACT;AACA,gBAAU,SAAS;AAAA,QACjB,CAAC,MAAM,SAAS,KAAK,GAAG;AAAA,QACxB,UAAU;AAAA,MACZ,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AACA,QAAMK,WAAU,WAAW;AAC3B,QAAM,WAAWA,SAAQ;AACzB,MAAI,aAAa,CAAC,WAAW,YAAY,OAAO,WAAW;AACzD,WAAO,QAAQ,OAAO,OAAO,OAAO,UAAU;AAAA,EAChD;AACA,SAAO,cAAc,KAAK;AAC1B,SAAO,aAAaL,UAAS;AAC7B,SAAO,kBAAkB,UAAU;AACnC,SAAO,oBAAoB;AAC3B,SAAO,KAAK,yBAAyB,OAAO,QAAQ;AACpD,SAAO,gBAAgB,cAAc,SAAS;AAC9C,MAAI,UAAU,GAAG;AACf,WAAO,cAAc,cAAc,SAAS;AAAA,EAC9C,WAAW,CAAC,OAAO,WAAW;AAC5B,WAAO,YAAY;AACnB,QAAI,CAAC,OAAO,+BAA+B;AACzC,aAAO,gCAAgC,SAASI,eAAc,GAAG;AAC/D,YAAI,CAAC,UAAU,OAAO,UAAW;AACjC,YAAI,EAAE,WAAW,KAAM;AACvB,eAAO,UAAU,oBAAoB,iBAAiB,OAAO,6BAA6B;AAC1F,eAAO,gCAAgC;AACvC,eAAO,OAAO;AACd,eAAO,cAAc,cAAc,SAAS;AAAA,MAC9C;AAAA,IACF;AACA,WAAO,UAAU,iBAAiB,iBAAiB,OAAO,6BAA6B;AAAA,EACzF;AACA,SAAO;AACT;AACA,SAAS,YAAY,OAAO,OAAO,cAAc,UAAU;AACzD,MAAI,UAAU,QAAQ;AACpB,YAAQ;AAAA,EACV;AACA,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,gBAAgB,SAAS,OAAO,EAAE;AACxC,YAAQ;AAAA,EACV;AACA,QAAM,SAAS;AACf,MAAI,OAAO,UAAW;AACtB,MAAI,OAAO,UAAU,aAAa;AAChC,YAAQ,OAAO,OAAO;AAAA,EACxB;AACA,QAAM,cAAc,OAAO,QAAQ,OAAO,OAAO,QAAQ,OAAO,OAAO,KAAK,OAAO;AACnF,MAAI,WAAW;AACf,MAAI,OAAO,OAAO,MAAM;AACtB,QAAI,OAAO,WAAW,OAAO,OAAO,QAAQ,SAAS;AAEnD,iBAAW,WAAW,OAAO,QAAQ;AAAA,IACvC,OAAO;AACL,UAAI;AACJ,UAAI,aAAa;AACf,cAAM,aAAa,WAAW,OAAO,OAAO,KAAK;AACjD,2BAAmB,OAAO,OAAO,KAAK,aAAW,QAAQ,aAAa,yBAAyB,IAAI,MAAM,UAAU,EAAE;AAAA,MACvH,OAAO;AACL,2BAAmB,OAAO,oBAAoB,QAAQ;AAAA,MACxD;AACA,YAAM,OAAO,cAAc,KAAK,KAAK,OAAO,OAAO,SAAS,OAAO,OAAO,KAAK,IAAI,IAAI,OAAO,OAAO;AACrG,YAAM;AAAA,QACJ;AAAA,MACF,IAAI,OAAO;AACX,UAAI,gBAAgB,OAAO,OAAO;AAClC,UAAI,kBAAkB,QAAQ;AAC5B,wBAAgB,OAAO,qBAAqB;AAAA,MAC9C,OAAO;AACL,wBAAgB,KAAK,KAAK,WAAW,OAAO,OAAO,eAAe,EAAE,CAAC;AACrE,YAAI,kBAAkB,gBAAgB,MAAM,GAAG;AAC7C,0BAAgB,gBAAgB;AAAA,QAClC;AAAA,MACF;AACA,UAAI,cAAc,OAAO,mBAAmB;AAC5C,UAAI,gBAAgB;AAClB,sBAAc,eAAe,mBAAmB,KAAK,KAAK,gBAAgB,CAAC;AAAA,MAC7E;AACA,UAAI,YAAY,kBAAkB,OAAO,OAAO,kBAAkB,UAAU,CAAC,aAAa;AACxF,sBAAc;AAAA,MAChB;AACA,UAAI,aAAa;AACf,cAAM,YAAY,iBAAiB,mBAAmB,OAAO,cAAc,SAAS,SAAS,mBAAmB,OAAO,cAAc,IAAI,OAAO,OAAO,gBAAgB,SAAS;AAChL,eAAO,QAAQ;AAAA,UACb;AAAA,UACA,SAAS;AAAA,UACT,kBAAkB,cAAc,SAAS,mBAAmB,IAAI,mBAAmB,OAAO;AAAA,UAC1F,gBAAgB,cAAc,SAAS,OAAO,YAAY;AAAA,QAC5D,CAAC;AAAA,MACH;AACA,UAAI,aAAa;AACf,cAAM,aAAa,WAAW,OAAO,OAAO,KAAK;AACjD,mBAAW,OAAO,OAAO,KAAK,aAAW,QAAQ,aAAa,yBAAyB,IAAI,MAAM,UAAU,EAAE;AAAA,MAC/G,OAAO;AACL,mBAAW,OAAO,oBAAoB,QAAQ;AAAA,MAChD;AAAA,IACF;AAAA,EACF;AACA,wBAAsB,MAAM;AAC1B,WAAO,QAAQ,UAAU,OAAO,cAAc,QAAQ;AAAA,EACxD,CAAC;AACD,SAAO;AACT;AAGA,SAAS,UAAU,OAAO,cAAc,UAAU;AAChD,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,WAAW,OAAO,UAAW,QAAO;AACzC,MAAI,OAAO,UAAU,aAAa;AAChC,YAAQ,OAAO,OAAO;AAAA,EACxB;AACA,MAAI,WAAW,OAAO;AACtB,MAAI,OAAO,kBAAkB,UAAU,OAAO,mBAAmB,KAAK,OAAO,oBAAoB;AAC/F,eAAW,KAAK,IAAI,OAAO,qBAAqB,WAAW,IAAI,GAAG,CAAC;AAAA,EACrE;AACA,QAAM,YAAY,OAAO,cAAc,OAAO,qBAAqB,IAAI;AACvE,QAAM,YAAY,OAAO,WAAW,OAAO,QAAQ;AACnD,MAAI,OAAO,MAAM;AACf,QAAI,aAAa,CAAC,aAAa,OAAO,oBAAqB,QAAO;AAClE,WAAO,QAAQ;AAAA,MACb,WAAW;AAAA,IACb,CAAC;AAED,WAAO,cAAc,OAAO,UAAU;AACtC,QAAI,OAAO,gBAAgB,OAAO,OAAO,SAAS,KAAK,OAAO,SAAS;AACrE,4BAAsB,MAAM;AAC1B,eAAO,QAAQ,OAAO,cAAc,WAAW,OAAO,cAAc,QAAQ;AAAA,MAC9E,CAAC;AACD,aAAO;AAAA,IACT;AAAA,EACF;AACA,MAAI,OAAO,UAAU,OAAO,OAAO;AACjC,WAAO,OAAO,QAAQ,GAAG,OAAO,cAAc,QAAQ;AAAA,EACxD;AACA,SAAO,OAAO,QAAQ,OAAO,cAAc,WAAW,OAAO,cAAc,QAAQ;AACrF;AAGA,SAAS,UAAU,OAAO,cAAc,UAAU;AAChD,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,WAAW,OAAO,UAAW,QAAO;AACzC,MAAI,OAAO,UAAU,aAAa;AAChC,YAAQ,OAAO,OAAO;AAAA,EACxB;AACA,QAAM,YAAY,OAAO,WAAW,OAAO,QAAQ;AACnD,MAAI,OAAO,MAAM;AACf,QAAI,aAAa,CAAC,aAAa,OAAO,oBAAqB,QAAO;AAClE,WAAO,QAAQ;AAAA,MACb,WAAW;AAAA,IACb,CAAC;AAED,WAAO,cAAc,OAAO,UAAU;AAAA,EACxC;AACA,QAAMJ,aAAY,eAAe,OAAO,YAAY,CAAC,OAAO;AAC5D,WAAS,UAAU,KAAK;AACtB,QAAI,MAAM,EAAG,QAAO,CAAC,KAAK,MAAM,KAAK,IAAI,GAAG,CAAC;AAC7C,WAAO,KAAK,MAAM,GAAG;AAAA,EACvB;AACA,QAAM,sBAAsB,UAAUA,UAAS;AAC/C,QAAM,qBAAqB,SAAS,IAAI,SAAO,UAAU,GAAG,CAAC;AAC7D,QAAM,aAAa,OAAO,YAAY,OAAO,SAAS;AACtD,MAAI,WAAW,SAAS,mBAAmB,QAAQ,mBAAmB,IAAI,CAAC;AAC3E,MAAI,OAAO,aAAa,gBAAgB,OAAO,WAAW,aAAa;AACrE,QAAI;AACJ,aAAS,QAAQ,CAAC,MAAM,cAAc;AACpC,UAAI,uBAAuB,MAAM;AAE/B,wBAAgB;AAAA,MAClB;AAAA,IACF,CAAC;AACD,QAAI,OAAO,kBAAkB,aAAa;AACxC,iBAAW,aAAa,SAAS,aAAa,IAAI,SAAS,gBAAgB,IAAI,gBAAgB,IAAI,aAAa;AAAA,IAClH;AAAA,EACF;AACA,MAAI,YAAY;AAChB,MAAI,OAAO,aAAa,aAAa;AACnC,gBAAY,WAAW,QAAQ,QAAQ;AACvC,QAAI,YAAY,EAAG,aAAY,OAAO,cAAc;AACpD,QAAI,OAAO,kBAAkB,UAAU,OAAO,mBAAmB,KAAK,OAAO,oBAAoB;AAC/F,kBAAY,YAAY,OAAO,qBAAqB,YAAY,IAAI,IAAI;AACxE,kBAAY,KAAK,IAAI,WAAW,CAAC;AAAA,IACnC;AAAA,EACF;AACA,MAAI,OAAO,UAAU,OAAO,aAAa;AACvC,UAAM,YAAY,OAAO,OAAO,WAAW,OAAO,OAAO,QAAQ,WAAW,OAAO,UAAU,OAAO,QAAQ,OAAO,SAAS,IAAI,OAAO,OAAO,SAAS;AACvJ,WAAO,OAAO,QAAQ,WAAW,OAAO,cAAc,QAAQ;AAAA,EAChE,WAAW,OAAO,QAAQ,OAAO,gBAAgB,KAAK,OAAO,SAAS;AACpE,0BAAsB,MAAM;AAC1B,aAAO,QAAQ,WAAW,OAAO,cAAc,QAAQ;AAAA,IACzD,CAAC;AACD,WAAO;AAAA,EACT;AACA,SAAO,OAAO,QAAQ,WAAW,OAAO,cAAc,QAAQ;AAChE;AAGA,SAAS,WAAW,OAAO,cAAc,UAAU;AACjD,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,QAAM,SAAS;AACf,MAAI,OAAO,UAAW;AACtB,MAAI,OAAO,UAAU,aAAa;AAChC,YAAQ,OAAO,OAAO;AAAA,EACxB;AACA,SAAO,OAAO,QAAQ,OAAO,aAAa,OAAO,cAAc,QAAQ;AACzE;AAGA,SAAS,eAAe,OAAO,cAAc,UAAU,WAAW;AAChE,MAAI,iBAAiB,QAAQ;AAC3B,mBAAe;AAAA,EACjB;AACA,MAAI,cAAc,QAAQ;AACxB,gBAAY;AAAA,EACd;AACA,QAAM,SAAS;AACf,MAAI,OAAO,UAAW;AACtB,MAAI,OAAO,UAAU,aAAa;AAChC,YAAQ,OAAO,OAAO;AAAA,EACxB;AACA,MAAI,QAAQ,OAAO;AACnB,QAAM,OAAO,KAAK,IAAI,OAAO,OAAO,oBAAoB,KAAK;AAC7D,QAAM,YAAY,OAAO,KAAK,OAAO,QAAQ,QAAQ,OAAO,OAAO,cAAc;AACjF,QAAMA,aAAY,OAAO,eAAe,OAAO,YAAY,CAAC,OAAO;AACnE,MAAIA,cAAa,OAAO,SAAS,SAAS,GAAG;AAG3C,UAAM,cAAc,OAAO,SAAS,SAAS;AAC7C,UAAM,WAAW,OAAO,SAAS,YAAY,CAAC;AAC9C,QAAIA,aAAY,eAAe,WAAW,eAAe,WAAW;AAClE,eAAS,OAAO,OAAO;AAAA,IACzB;AAAA,EACF,OAAO;AAGL,UAAM,WAAW,OAAO,SAAS,YAAY,CAAC;AAC9C,UAAM,cAAc,OAAO,SAAS,SAAS;AAC7C,QAAIA,aAAY,aAAa,cAAc,YAAY,WAAW;AAChE,eAAS,OAAO,OAAO;AAAA,IACzB;AAAA,EACF;AACA,UAAQ,KAAK,IAAI,OAAO,CAAC;AACzB,UAAQ,KAAK,IAAI,OAAO,OAAO,WAAW,SAAS,CAAC;AACpD,SAAO,OAAO,QAAQ,OAAO,OAAO,cAAc,QAAQ;AAC5D;AACA,SAAS,sBAAsB;AAC7B,QAAM,SAAS;AACf,MAAI,OAAO,UAAW;AACtB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,gBAAgB,OAAO,kBAAkB,SAAS,OAAO,qBAAqB,IAAI,OAAO;AAC/F,MAAI,eAAe,OAAO,sBAAsB,OAAO,YAAY;AACnE,MAAI;AACJ,QAAM,gBAAgB,OAAO,YAAY,iBAAiB,IAAI,OAAO,UAAU;AAC/E,QAAM,SAAS,OAAO,QAAQ,OAAO,OAAO,QAAQ,OAAO,OAAO,KAAK,OAAO;AAC9E,MAAI,OAAO,MAAM;AACf,QAAI,OAAO,UAAW;AACtB,gBAAY,SAAS,OAAO,aAAa,aAAa,yBAAyB,GAAG,EAAE;AACpF,QAAI,OAAO,gBAAgB;AACzB,aAAO,YAAY,SAAS;AAAA,IAC9B,WAAW,gBAAgB,UAAU,OAAO,OAAO,SAAS,iBAAiB,KAAK,OAAO,OAAO,KAAK,OAAO,KAAK,OAAO,OAAO,SAAS,gBAAgB;AACtJ,aAAO,QAAQ;AACf,qBAAe,OAAO,cAAc,gBAAgB,UAAU,GAAG,aAAa,6BAA6B,SAAS,IAAI,EAAE,CAAC,CAAC;AAC5H,eAAS,MAAM;AACb,eAAO,QAAQ,YAAY;AAAA,MAC7B,CAAC;AAAA,IACH,OAAO;AACL,aAAO,QAAQ,YAAY;AAAA,IAC7B;AAAA,EACF,OAAO;AACL,WAAO,QAAQ,YAAY;AAAA,EAC7B;AACF;AACA,IAAI,QAAQ;AAAA,EACV;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,WAAW,gBAAgB,SAAS;AAC3C,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,OAAO,QAAQ,OAAO,WAAW,OAAO,OAAO,QAAQ,QAAS;AACrE,QAAM,aAAa,MAAM;AACvB,UAAM,SAAS,gBAAgB,UAAU,IAAI,OAAO,UAAU,gBAAgB;AAC9E,WAAO,QAAQ,CAAC,IAAI,UAAU;AAC5B,SAAG,aAAa,2BAA2B,KAAK;AAAA,IAClD,CAAC;AAAA,EACH;AACA,QAAM,mBAAmB,MAAM;AAC7B,UAAM,SAAS,gBAAgB,UAAU,IAAI,OAAO,eAAe,EAAE;AACrE,WAAO,QAAQ,QAAM;AACnB,SAAG,OAAO;AAAA,IACZ,CAAC;AACD,QAAI,OAAO,SAAS,GAAG;AACrB,aAAO,aAAa;AACpB,aAAO,aAAa;AAAA,IACtB;AAAA,EACF;AACA,QAAM,cAAc,OAAO,QAAQ,OAAO,QAAQ,OAAO,KAAK,OAAO;AACrE,MAAI,OAAO,uBAAuB,OAAO,iBAAiB,KAAK,cAAc;AAC3E,qBAAiB;AAAA,EACnB;AACA,QAAM,iBAAiB,OAAO,kBAAkB,cAAc,OAAO,KAAK,OAAO;AACjF,QAAM,kBAAkB,OAAO,OAAO,SAAS,mBAAmB;AAClE,QAAM,iBAAiB,eAAe,OAAO,OAAO,SAAS,OAAO,KAAK,SAAS;AAClF,QAAM,iBAAiB,oBAAkB;AACvC,aAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK,GAAG;AAC1C,YAAM,UAAU,OAAO,YAAY,cAAc,gBAAgB,CAAC,OAAO,eAAe,CAAC,IAAI,cAAc,OAAO,CAAC,OAAO,YAAY,OAAO,eAAe,CAAC;AAC7J,aAAO,SAAS,OAAO,OAAO;AAAA,IAChC;AAAA,EACF;AACA,MAAI,iBAAiB;AACnB,QAAI,OAAO,oBAAoB;AAC7B,YAAM,cAAc,iBAAiB,OAAO,OAAO,SAAS;AAC5D,qBAAe,WAAW;AAC1B,aAAO,aAAa;AACpB,aAAO,aAAa;AAAA,IACtB,OAAO;AACL,kBAAY,iLAAiL;AAAA,IAC/L;AACA,eAAW;AAAA,EACb,WAAW,gBAAgB;AACzB,QAAI,OAAO,oBAAoB;AAC7B,YAAM,cAAc,OAAO,KAAK,OAAO,OAAO,OAAO,SAAS,OAAO,KAAK;AAC1E,qBAAe,WAAW;AAC1B,aAAO,aAAa;AACpB,aAAO,aAAa;AAAA,IACtB,OAAO;AACL,kBAAY,4KAA4K;AAAA,IAC1L;AACA,eAAW;AAAA,EACb,OAAO;AACL,eAAW;AAAA,EACb;AACA,SAAO,QAAQ;AAAA,IACb;AAAA,IACA,WAAW,OAAO,iBAAiB,SAAY;AAAA,IAC/C;AAAA,EACF,CAAC;AACH;AACA,SAAS,QAAQ,OAAO;AACtB,MAAI;AAAA,IACF;AAAA,IACA,SAAAM,WAAU;AAAA,IACV;AAAA,IACA,cAAAC;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI,UAAU,SAAS,CAAC,IAAI;AAC5B,QAAM,SAAS;AACf,MAAI,CAAC,OAAO,OAAO,KAAM;AACzB,SAAO,KAAK,eAAe;AAC3B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,SAAO,iBAAiB;AACxB,SAAO,iBAAiB;AACxB,MAAI,OAAO,WAAW,OAAO,QAAQ,SAAS;AAC5C,QAAID,UAAS;AACX,UAAI,CAAC,OAAO,kBAAkB,OAAO,cAAc,GAAG;AACpD,eAAO,QAAQ,OAAO,QAAQ,OAAO,QAAQ,GAAG,OAAO,IAAI;AAAA,MAC7D,WAAW,OAAO,kBAAkB,OAAO,YAAY,OAAO,eAAe;AAC3E,eAAO,QAAQ,OAAO,QAAQ,OAAO,SAAS,OAAO,WAAW,GAAG,OAAO,IAAI;AAAA,MAChF,WAAW,OAAO,cAAc,OAAO,SAAS,SAAS,GAAG;AAC1D,eAAO,QAAQ,OAAO,QAAQ,cAAc,GAAG,OAAO,IAAI;AAAA,MAC5D;AAAA,IACF;AACA,WAAO,iBAAiB;AACxB,WAAO,iBAAiB;AACxB,WAAO,KAAK,SAAS;AACrB;AAAA,EACF;AACA,MAAI,gBAAgB,OAAO;AAC3B,MAAI,kBAAkB,QAAQ;AAC5B,oBAAgB,OAAO,qBAAqB;AAAA,EAC9C,OAAO;AACL,oBAAgB,KAAK,KAAK,WAAW,OAAO,eAAe,EAAE,CAAC;AAC9D,QAAI,kBAAkB,gBAAgB,MAAM,GAAG;AAC7C,sBAAgB,gBAAgB;AAAA,IAClC;AAAA,EACF;AACA,QAAM,iBAAiB,OAAO,qBAAqB,gBAAgB,OAAO;AAC1E,MAAI,eAAe,iBAAiB,KAAK,IAAI,gBAAgB,KAAK,KAAK,gBAAgB,CAAC,CAAC,IAAI;AAC7F,MAAI,eAAe,mBAAmB,GAAG;AACvC,oBAAgB,iBAAiB,eAAe;AAAA,EAClD;AACA,kBAAgB,OAAO;AACvB,SAAO,eAAe;AACtB,QAAM,cAAc,OAAO,QAAQ,OAAO,QAAQ,OAAO,KAAK,OAAO;AACrE,MAAI,OAAO,SAAS,gBAAgB,gBAAgB,OAAO,OAAO,WAAW,WAAW,OAAO,SAAS,gBAAgB,eAAe,GAAG;AACxI,gBAAY,0OAA0O;AAAA,EACxP,WAAW,eAAe,OAAO,KAAK,SAAS,OAAO;AACpD,gBAAY,yEAAyE;AAAA,EACvF;AACA,QAAM,uBAAuB,CAAC;AAC9B,QAAM,sBAAsB,CAAC;AAC7B,QAAM,OAAO,cAAc,KAAK,KAAK,OAAO,SAAS,OAAO,KAAK,IAAI,IAAI,OAAO;AAChF,QAAM,oBAAoB,WAAW,OAAO,eAAe,iBAAiB,CAAC;AAC7E,MAAI,cAAc,oBAAoB,eAAe,OAAO;AAC5D,MAAI,OAAO,qBAAqB,aAAa;AAC3C,uBAAmB,OAAO,cAAc,OAAO,KAAK,QAAM,GAAG,UAAU,SAAS,OAAO,gBAAgB,CAAC,CAAC;AAAA,EAC3G,OAAO;AACL,kBAAc;AAAA,EAChB;AACA,QAAM,SAAS,cAAc,UAAU,CAAC;AACxC,QAAM,SAAS,cAAc,UAAU,CAAC;AACxC,MAAI,kBAAkB;AACtB,MAAI,iBAAiB;AACrB,QAAM,iBAAiB,cAAc,OAAO,gBAAgB,EAAE,SAAS;AACvE,QAAM,0BAA0B,kBAAkB,kBAAkB,OAAOC,kBAAiB,cAAc,CAAC,gBAAgB,IAAI,MAAM;AAErI,MAAI,0BAA0B,cAAc;AAC1C,sBAAkB,KAAK,IAAI,eAAe,yBAAyB,cAAc;AACjF,aAAS,IAAI,GAAG,IAAI,eAAe,yBAAyB,KAAK,GAAG;AAClE,YAAM,QAAQ,IAAI,KAAK,MAAM,IAAI,IAAI,IAAI;AACzC,UAAI,aAAa;AACf,cAAM,oBAAoB,OAAO,QAAQ;AACzC,iBAASC,KAAI,OAAO,SAAS,GAAGA,MAAK,GAAGA,MAAK,GAAG;AAC9C,cAAI,OAAOA,EAAC,EAAE,WAAW,kBAAmB,sBAAqB,KAAKA,EAAC;AAAA,QACzE;AAAA,MAIF,OAAO;AACL,6BAAqB,KAAK,OAAO,QAAQ,CAAC;AAAA,MAC5C;AAAA,IACF;AAAA,EACF,WAAW,0BAA0B,gBAAgB,OAAO,cAAc;AACxE,qBAAiB,KAAK,IAAI,2BAA2B,OAAO,eAAe,IAAI,cAAc;AAC7F,QAAI,mBAAmB;AACrB,uBAAiB,KAAK,IAAI,gBAAgB,gBAAgB,OAAO,eAAe,CAAC;AAAA,IACnF;AACA,aAAS,IAAI,GAAG,IAAI,gBAAgB,KAAK,GAAG;AAC1C,YAAM,QAAQ,IAAI,KAAK,MAAM,IAAI,IAAI,IAAI;AACzC,UAAI,aAAa;AACf,eAAO,QAAQ,CAACT,QAAO,eAAe;AACpC,cAAIA,OAAM,WAAW,MAAO,qBAAoB,KAAK,UAAU;AAAA,QACjE,CAAC;AAAA,MACH,OAAO;AACL,4BAAoB,KAAK,KAAK;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AACA,SAAO,sBAAsB;AAC7B,wBAAsB,MAAM;AAC1B,WAAO,sBAAsB;AAAA,EAC/B,CAAC;AACD,MAAI,OAAO,OAAO,WAAW,WAAW,OAAO,SAAS,gBAAgB,eAAe,GAAG;AACxF,QAAI,oBAAoB,SAAS,gBAAgB,GAAG;AAClD,0BAAoB,OAAO,oBAAoB,QAAQ,gBAAgB,GAAG,CAAC;AAAA,IAC7E;AACA,QAAI,qBAAqB,SAAS,gBAAgB,GAAG;AACnD,2BAAqB,OAAO,qBAAqB,QAAQ,gBAAgB,GAAG,CAAC;AAAA,IAC/E;AAAA,EACF;AACA,MAAI,QAAQ;AACV,yBAAqB,QAAQ,WAAS;AACpC,aAAO,KAAK,EAAE,oBAAoB;AAClC,eAAS,QAAQ,OAAO,KAAK,CAAC;AAC9B,aAAO,KAAK,EAAE,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACA,MAAI,QAAQ;AACV,wBAAoB,QAAQ,WAAS;AACnC,aAAO,KAAK,EAAE,oBAAoB;AAClC,eAAS,OAAO,OAAO,KAAK,CAAC;AAC7B,aAAO,KAAK,EAAE,oBAAoB;AAAA,IACpC,CAAC;AAAA,EACH;AACA,SAAO,aAAa;AACpB,MAAI,OAAO,kBAAkB,QAAQ;AACnC,WAAO,aAAa;AAAA,EACtB,WAAW,gBAAgB,qBAAqB,SAAS,KAAK,UAAU,oBAAoB,SAAS,KAAK,SAAS;AACjH,WAAO,OAAO,QAAQ,CAACA,QAAO,eAAe;AAC3C,aAAO,KAAK,YAAY,YAAYA,QAAO,OAAO,MAAM;AAAA,IAC1D,CAAC;AAAA,EACH;AACA,MAAI,OAAO,qBAAqB;AAC9B,WAAO,mBAAmB;AAAA,EAC5B;AACA,MAAIO,UAAS;AACX,QAAI,qBAAqB,SAAS,KAAK,QAAQ;AAC7C,UAAI,OAAO,mBAAmB,aAAa;AACzC,cAAM,wBAAwB,OAAO,WAAW,WAAW;AAC3D,cAAM,oBAAoB,OAAO,WAAW,cAAc,eAAe;AACzE,cAAM,OAAO,oBAAoB;AACjC,YAAI,cAAc;AAChB,iBAAO,aAAa,OAAO,YAAY,IAAI;AAAA,QAC7C,OAAO;AACL,iBAAO,QAAQ,cAAc,KAAK,KAAK,eAAe,GAAG,GAAG,OAAO,IAAI;AACvE,cAAIC,eAAc;AAChB,mBAAO,gBAAgB,iBAAiB,OAAO,gBAAgB,iBAAiB;AAChF,mBAAO,gBAAgB,mBAAmB,OAAO,gBAAgB,mBAAmB;AAAA,UACtF;AAAA,QACF;AAAA,MACF,OAAO;AACL,YAAIA,eAAc;AAChB,gBAAM,QAAQ,cAAc,qBAAqB,SAAS,OAAO,KAAK,OAAO,qBAAqB;AAClG,iBAAO,QAAQ,OAAO,cAAc,OAAO,GAAG,OAAO,IAAI;AACzD,iBAAO,gBAAgB,mBAAmB,OAAO;AAAA,QACnD;AAAA,MACF;AAAA,IACF,WAAW,oBAAoB,SAAS,KAAK,QAAQ;AACnD,UAAI,OAAO,mBAAmB,aAAa;AACzC,cAAM,wBAAwB,OAAO,WAAW,WAAW;AAC3D,cAAM,oBAAoB,OAAO,WAAW,cAAc,cAAc;AACxE,cAAM,OAAO,oBAAoB;AACjC,YAAI,cAAc;AAChB,iBAAO,aAAa,OAAO,YAAY,IAAI;AAAA,QAC7C,OAAO;AACL,iBAAO,QAAQ,cAAc,gBAAgB,GAAG,OAAO,IAAI;AAC3D,cAAIA,eAAc;AAChB,mBAAO,gBAAgB,iBAAiB,OAAO,gBAAgB,iBAAiB;AAChF,mBAAO,gBAAgB,mBAAmB,OAAO,gBAAgB,mBAAmB;AAAA,UACtF;AAAA,QACF;AAAA,MACF,OAAO;AACL,cAAM,QAAQ,cAAc,oBAAoB,SAAS,OAAO,KAAK,OAAO,oBAAoB;AAChG,eAAO,QAAQ,OAAO,cAAc,OAAO,GAAG,OAAO,IAAI;AAAA,MAC3D;AAAA,IACF;AAAA,EACF;AACA,SAAO,iBAAiB;AACxB,SAAO,iBAAiB;AACxB,MAAI,OAAO,cAAc,OAAO,WAAW,WAAW,CAAC,cAAc;AACnE,UAAM,aAAa;AAAA,MACjB;AAAA,MACA;AAAA,MACA,cAAAA;AAAA,MACA;AAAA,MACA,cAAc;AAAA,IAChB;AACA,QAAI,MAAM,QAAQ,OAAO,WAAW,OAAO,GAAG;AAC5C,aAAO,WAAW,QAAQ,QAAQ,OAAK;AACrC,YAAI,CAAC,EAAE,aAAa,EAAE,OAAO,KAAM,GAAE,QAAQ,iCACxC,aADwC;AAAA,UAE3C,SAAS,EAAE,OAAO,kBAAkB,OAAO,gBAAgBD,WAAU;AAAA,QACvE,EAAC;AAAA,MACH,CAAC;AAAA,IACH,WAAW,OAAO,WAAW,mBAAmB,OAAO,eAAe,OAAO,WAAW,QAAQ,OAAO,MAAM;AAC3G,aAAO,WAAW,QAAQ,QAAQ,iCAC7B,aAD6B;AAAA,QAEhC,SAAS,OAAO,WAAW,QAAQ,OAAO,kBAAkB,OAAO,gBAAgBA,WAAU;AAAA,MAC/F,EAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO,KAAK,SAAS;AACvB;AACA,SAAS,cAAc;AACrB,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,OAAO,QAAQ,CAAC,YAAY,OAAO,WAAW,OAAO,OAAO,QAAQ,QAAS;AAClF,SAAO,aAAa;AACpB,QAAM,iBAAiB,CAAC;AACxB,SAAO,OAAO,QAAQ,aAAW;AAC/B,UAAM,QAAQ,OAAO,QAAQ,qBAAqB,cAAc,QAAQ,aAAa,yBAAyB,IAAI,IAAI,QAAQ;AAC9H,mBAAe,KAAK,IAAI;AAAA,EAC1B,CAAC;AACD,SAAO,OAAO,QAAQ,aAAW;AAC/B,YAAQ,gBAAgB,yBAAyB;AAAA,EACnD,CAAC;AACD,iBAAe,QAAQ,aAAW;AAChC,aAAS,OAAO,OAAO;AAAA,EACzB,CAAC;AACD,SAAO,aAAa;AACpB,SAAO,QAAQ,OAAO,WAAW,CAAC;AACpC;AACA,IAAI,OAAO;AAAA,EACT;AAAA,EACA;AAAA,EACA;AACF;AACA,SAAS,cAAc,QAAQ;AAC7B,QAAM,SAAS;AACf,MAAI,CAAC,OAAO,OAAO,iBAAiB,OAAO,OAAO,iBAAiB,OAAO,YAAY,OAAO,OAAO,QAAS;AAC7G,QAAM,KAAK,OAAO,OAAO,sBAAsB,cAAc,OAAO,KAAK,OAAO;AAChF,MAAI,OAAO,WAAW;AACpB,WAAO,sBAAsB;AAAA,EAC/B;AACA,KAAG,MAAM,SAAS;AAClB,KAAG,MAAM,SAAS,SAAS,aAAa;AACxC,MAAI,OAAO,WAAW;AACpB,0BAAsB,MAAM;AAC1B,aAAO,sBAAsB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AACA,SAAS,kBAAkB;AACzB,QAAM,SAAS;AACf,MAAI,OAAO,OAAO,iBAAiB,OAAO,YAAY,OAAO,OAAO,SAAS;AAC3E;AAAA,EACF;AACA,MAAI,OAAO,WAAW;AACpB,WAAO,sBAAsB;AAAA,EAC/B;AACA,SAAO,OAAO,OAAO,sBAAsB,cAAc,OAAO,WAAW,EAAE,MAAM,SAAS;AAC5F,MAAI,OAAO,WAAW;AACpB,0BAAsB,MAAM;AAC1B,aAAO,sBAAsB;AAAA,IAC/B,CAAC;AAAA,EACH;AACF;AACA,IAAI,aAAa;AAAA,EACf;AAAA,EACA;AACF;AAGA,SAAS,eAAe,UAAU,MAAM;AACtC,MAAI,SAAS,QAAQ;AACnB,WAAO;AAAA,EACT;AACA,WAAS,cAAc,IAAI;AACzB,QAAI,CAAC,MAAM,OAAO,YAAY,KAAK,OAAO,UAAU,EAAG,QAAO;AAC9D,QAAI,GAAG,aAAc,MAAK,GAAG;AAC7B,UAAM,QAAQ,GAAG,QAAQ,QAAQ;AACjC,QAAI,CAAC,SAAS,CAAC,GAAG,aAAa;AAC7B,aAAO;AAAA,IACT;AACA,WAAO,SAAS,cAAc,GAAG,YAAY,EAAE,IAAI;AAAA,EACrD;AACA,SAAO,cAAc,IAAI;AAC3B;AACA,SAAS,iBAAiB,QAAQ,OAAO,QAAQ;AAC/C,QAAMZ,UAAS,UAAU;AACzB,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,QAAM,qBAAqB,OAAO;AAClC,QAAM,qBAAqB,OAAO;AAClC,MAAI,uBAAuB,UAAU,sBAAsB,UAAUA,QAAO,aAAa,qBAAqB;AAC5G,QAAI,uBAAuB,WAAW;AACpC,YAAM,eAAe;AACrB,aAAO;AAAA,IACT;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AACA,SAAS,aAAa,OAAO;AAC3B,QAAM,SAAS;AACf,QAAMC,YAAW,YAAY;AAC7B,MAAI,IAAI;AACR,MAAI,EAAE,cAAe,KAAI,EAAE;AAC3B,QAAM,OAAO,OAAO;AACpB,MAAI,EAAE,SAAS,eAAe;AAC5B,QAAI,KAAK,cAAc,QAAQ,KAAK,cAAc,EAAE,WAAW;AAC7D;AAAA,IACF;AACA,SAAK,YAAY,EAAE;AAAA,EACrB,WAAW,EAAE,SAAS,gBAAgB,EAAE,cAAc,WAAW,GAAG;AAClE,SAAK,UAAU,EAAE,cAAc,CAAC,EAAE;AAAA,EACpC;AACA,MAAI,EAAE,SAAS,cAAc;AAE3B,qBAAiB,QAAQ,GAAG,EAAE,cAAc,CAAC,EAAE,KAAK;AACpD;AAAA,EACF;AACA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,QAAS;AACd,MAAI,CAAC,OAAO,iBAAiB,EAAE,gBAAgB,QAAS;AACxD,MAAI,OAAO,aAAa,OAAO,gCAAgC;AAC7D;AAAA,EACF;AACA,MAAI,CAAC,OAAO,aAAa,OAAO,WAAW,OAAO,MAAM;AACtD,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,WAAW,EAAE;AACjB,MAAI,OAAO,sBAAsB,WAAW;AAC1C,QAAI,CAAC,iBAAiB,UAAU,OAAO,SAAS,EAAG;AAAA,EACrD;AACA,MAAI,WAAW,KAAK,EAAE,UAAU,EAAG;AACnC,MAAI,YAAY,KAAK,EAAE,SAAS,EAAG;AACnC,MAAI,KAAK,aAAa,KAAK,QAAS;AAGpC,QAAM,uBAAuB,CAAC,CAAC,OAAO,kBAAkB,OAAO,mBAAmB;AAElF,QAAM,YAAY,EAAE,eAAe,EAAE,aAAa,IAAI,EAAE;AACxD,MAAI,wBAAwB,EAAE,UAAU,EAAE,OAAO,cAAc,WAAW;AACxE,eAAW,UAAU,CAAC;AAAA,EACxB;AACA,QAAM,oBAAoB,OAAO,oBAAoB,OAAO,oBAAoB,IAAI,OAAO,cAAc;AACzG,QAAM,iBAAiB,CAAC,EAAE,EAAE,UAAU,EAAE,OAAO;AAG/C,MAAI,OAAO,cAAc,iBAAiB,eAAe,mBAAmB,QAAQ,IAAI,SAAS,QAAQ,iBAAiB,IAAI;AAC5H,WAAO,aAAa;AACpB;AAAA,EACF;AACA,MAAI,OAAO,cAAc;AACvB,QAAI,CAAC,SAAS,QAAQ,OAAO,YAAY,EAAG;AAAA,EAC9C;AACA,UAAQ,WAAW,EAAE;AACrB,UAAQ,WAAW,EAAE;AACrB,QAAM,SAAS,QAAQ;AACvB,QAAM,SAAS,QAAQ;AAIvB,MAAI,CAAC,iBAAiB,QAAQ,GAAG,MAAM,GAAG;AACxC;AAAA,EACF;AACA,SAAO,OAAO,MAAM;AAAA,IAClB,WAAW;AAAA,IACX,SAAS;AAAA,IACT,qBAAqB;AAAA,IACrB,aAAa;AAAA,IACb,aAAa;AAAA,EACf,CAAC;AACD,UAAQ,SAAS;AACjB,UAAQ,SAAS;AACjB,OAAK,iBAAiB,IAAI;AAC1B,SAAO,aAAa;AACpB,SAAO,WAAW;AAClB,SAAO,iBAAiB;AACxB,MAAI,OAAO,YAAY,EAAG,MAAK,qBAAqB;AACpD,MAAI,iBAAiB;AACrB,MAAI,SAAS,QAAQ,KAAK,iBAAiB,GAAG;AAC5C,qBAAiB;AACjB,QAAI,SAAS,aAAa,UAAU;AAClC,WAAK,YAAY;AAAA,IACnB;AAAA,EACF;AACA,MAAIA,UAAS,iBAAiBA,UAAS,cAAc,QAAQ,KAAK,iBAAiB,KAAKA,UAAS,kBAAkB,aAAa,EAAE,gBAAgB,WAAW,EAAE,gBAAgB,WAAW,CAAC,SAAS,QAAQ,KAAK,iBAAiB,IAAI;AACpO,IAAAA,UAAS,cAAc,KAAK;AAAA,EAC9B;AACA,QAAM,uBAAuB,kBAAkB,OAAO,kBAAkB,OAAO;AAC/E,OAAK,OAAO,iCAAiC,yBAAyB,CAAC,SAAS,mBAAmB;AACjG,MAAE,eAAe;AAAA,EACnB;AACA,MAAI,OAAO,YAAY,OAAO,SAAS,WAAW,OAAO,YAAY,OAAO,aAAa,CAAC,OAAO,SAAS;AACxG,WAAO,SAAS,aAAa;AAAA,EAC/B;AACA,SAAO,KAAK,cAAc,CAAC;AAC7B;AACA,SAAS,YAAY,OAAO;AAC1B,QAAMA,YAAW,YAAY;AAC7B,QAAM,SAAS;AACf,QAAM,OAAO,OAAO;AACpB,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,QAAS;AACd,MAAI,CAAC,OAAO,iBAAiB,MAAM,gBAAgB,QAAS;AAC5D,MAAI,IAAI;AACR,MAAI,EAAE,cAAe,KAAI,EAAE;AAC3B,MAAI,EAAE,SAAS,eAAe;AAC5B,QAAI,KAAK,YAAY,KAAM;AAC3B,UAAM,KAAK,EAAE;AACb,QAAI,OAAO,KAAK,UAAW;AAAA,EAC7B;AACA,MAAI;AACJ,MAAI,EAAE,SAAS,aAAa;AAC1B,kBAAc,CAAC,GAAG,EAAE,cAAc,EAAE,KAAK,OAAK,EAAE,eAAe,KAAK,OAAO;AAC3E,QAAI,CAAC,eAAe,YAAY,eAAe,KAAK,QAAS;AAAA,EAC/D,OAAO;AACL,kBAAc;AAAA,EAChB;AACA,MAAI,CAAC,KAAK,WAAW;AACnB,QAAI,KAAK,eAAe,KAAK,aAAa;AACxC,aAAO,KAAK,qBAAqB,CAAC;AAAA,IACpC;AACA;AAAA,EACF;AACA,QAAM,QAAQ,YAAY;AAC1B,QAAM,QAAQ,YAAY;AAC1B,MAAI,EAAE,yBAAyB;AAC7B,YAAQ,SAAS;AACjB,YAAQ,SAAS;AACjB;AAAA,EACF;AACA,MAAI,CAAC,OAAO,gBAAgB;AAC1B,QAAI,CAAC,EAAE,OAAO,QAAQ,KAAK,iBAAiB,GAAG;AAC7C,aAAO,aAAa;AAAA,IACtB;AACA,QAAI,KAAK,WAAW;AAClB,aAAO,OAAO,SAAS;AAAA,QACrB,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,MACZ,CAAC;AACD,WAAK,iBAAiB,IAAI;AAAA,IAC5B;AACA;AAAA,EACF;AACA,MAAI,OAAO,uBAAuB,CAAC,OAAO,MAAM;AAC9C,QAAI,OAAO,WAAW,GAAG;AAEvB,UAAI,QAAQ,QAAQ,UAAU,OAAO,aAAa,OAAO,aAAa,KAAK,QAAQ,QAAQ,UAAU,OAAO,aAAa,OAAO,aAAa,GAAG;AAC9I,aAAK,YAAY;AACjB,aAAK,UAAU;AACf;AAAA,MACF;AAAA,IACF,WAAW,QAAQ,QAAQ,QAAQ,UAAU,CAAC,OAAO,aAAa,OAAO,aAAa,KAAK,QAAQ,QAAQ,UAAU,CAAC,OAAO,aAAa,OAAO,aAAa,IAAI;AAChK;AAAA,IACF,WAAW,CAAC,QAAQ,QAAQ,QAAQ,UAAU,OAAO,aAAa,OAAO,aAAa,KAAK,QAAQ,QAAQ,UAAU,OAAO,aAAa,OAAO,aAAa,IAAI;AAC/J;AAAA,IACF;AAAA,EACF;AACA,MAAIA,UAAS,iBAAiBA,UAAS,cAAc,QAAQ,KAAK,iBAAiB,KAAKA,UAAS,kBAAkB,EAAE,UAAU,EAAE,gBAAgB,SAAS;AACxJ,IAAAA,UAAS,cAAc,KAAK;AAAA,EAC9B;AACA,MAAIA,UAAS,eAAe;AAC1B,QAAI,EAAE,WAAWA,UAAS,iBAAiB,EAAE,OAAO,QAAQ,KAAK,iBAAiB,GAAG;AACnF,WAAK,UAAU;AACf,aAAO,aAAa;AACpB;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,qBAAqB;AAC5B,WAAO,KAAK,aAAa,CAAC;AAAA,EAC5B;AACA,UAAQ,YAAY,QAAQ;AAC5B,UAAQ,YAAY,QAAQ;AAC5B,UAAQ,WAAW;AACnB,UAAQ,WAAW;AACnB,QAAM,QAAQ,QAAQ,WAAW,QAAQ;AACzC,QAAM,QAAQ,QAAQ,WAAW,QAAQ;AACzC,MAAI,OAAO,OAAO,aAAa,KAAK,KAAK,SAAS,IAAI,SAAS,CAAC,IAAI,OAAO,OAAO,UAAW;AAC7F,MAAI,OAAO,KAAK,gBAAgB,aAAa;AAC3C,QAAI;AACJ,QAAI,OAAO,aAAa,KAAK,QAAQ,aAAa,QAAQ,UAAU,OAAO,WAAW,KAAK,QAAQ,aAAa,QAAQ,QAAQ;AAC9H,WAAK,cAAc;AAAA,IACrB,OAAO;AAEL,UAAI,QAAQ,QAAQ,QAAQ,SAAS,IAAI;AACvC,qBAAa,KAAK,MAAM,KAAK,IAAI,KAAK,GAAG,KAAK,IAAI,KAAK,CAAC,IAAI,MAAM,KAAK;AACvE,aAAK,cAAc,OAAO,aAAa,IAAI,aAAa,OAAO,aAAa,KAAK,aAAa,OAAO;AAAA,MACvG;AAAA,IACF;AAAA,EACF;AACA,MAAI,KAAK,aAAa;AACpB,WAAO,KAAK,qBAAqB,CAAC;AAAA,EACpC;AACA,MAAI,OAAO,KAAK,gBAAgB,aAAa;AAC3C,QAAI,QAAQ,aAAa,QAAQ,UAAU,QAAQ,aAAa,QAAQ,QAAQ;AAC9E,WAAK,cAAc;AAAA,IACrB;AAAA,EACF;AACA,MAAI,KAAK,eAAe,EAAE,SAAS,eAAe,KAAK,iCAAiC;AACtF,SAAK,YAAY;AACjB;AAAA,EACF;AACA,MAAI,CAAC,KAAK,aAAa;AACrB;AAAA,EACF;AACA,SAAO,aAAa;AACpB,MAAI,CAAC,OAAO,WAAW,EAAE,YAAY;AACnC,MAAE,eAAe;AAAA,EACnB;AACA,MAAI,OAAO,4BAA4B,CAAC,OAAO,QAAQ;AACrD,MAAE,gBAAgB;AAAA,EACpB;AACA,MAAI,OAAO,OAAO,aAAa,IAAI,QAAQ;AAC3C,MAAI,cAAc,OAAO,aAAa,IAAI,QAAQ,WAAW,QAAQ,YAAY,QAAQ,WAAW,QAAQ;AAC5G,MAAI,OAAO,gBAAgB;AACzB,WAAO,KAAK,IAAI,IAAI,KAAK,MAAM,IAAI;AACnC,kBAAc,KAAK,IAAI,WAAW,KAAK,MAAM,IAAI;AAAA,EACnD;AACA,UAAQ,OAAO;AACf,UAAQ,OAAO;AACf,MAAI,KAAK;AACP,WAAO,CAAC;AACR,kBAAc,CAAC;AAAA,EACjB;AACA,QAAM,uBAAuB,OAAO;AACpC,SAAO,iBAAiB,OAAO,IAAI,SAAS;AAC5C,SAAO,mBAAmB,cAAc,IAAI,SAAS;AACrD,QAAM,SAAS,OAAO,OAAO,QAAQ,CAAC,OAAO;AAC7C,QAAM,eAAe,OAAO,qBAAqB,UAAU,OAAO,kBAAkB,OAAO,qBAAqB,UAAU,OAAO;AACjI,MAAI,CAAC,KAAK,SAAS;AACjB,QAAI,UAAU,cAAc;AAC1B,aAAO,QAAQ;AAAA,QACb,WAAW,OAAO;AAAA,MACpB,CAAC;AAAA,IACH;AACA,SAAK,iBAAiB,OAAO,aAAa;AAC1C,WAAO,cAAc,CAAC;AACtB,QAAI,OAAO,WAAW;AACpB,YAAM,MAAM,IAAI,OAAO,YAAY,iBAAiB;AAAA,QAClD,SAAS;AAAA,QACT,YAAY;AAAA,QACZ,QAAQ;AAAA,UACN,mBAAmB;AAAA,QACrB;AAAA,MACF,CAAC;AACD,aAAO,UAAU,cAAc,GAAG;AAAA,IACpC;AACA,SAAK,sBAAsB;AAE3B,QAAI,OAAO,eAAe,OAAO,mBAAmB,QAAQ,OAAO,mBAAmB,OAAO;AAC3F,aAAO,cAAc,IAAI;AAAA,IAC3B;AACA,WAAO,KAAK,mBAAmB,CAAC;AAAA,EAClC;AACA,MAAI;AACJ,uBAAI,KAAK,GAAE,QAAQ;AACnB,MAAI,OAAO,mBAAmB,SAAS,KAAK,WAAW,KAAK,sBAAsB,yBAAyB,OAAO,oBAAoB,UAAU,gBAAgB,KAAK,IAAI,IAAI,KAAK,GAAG;AACnL,WAAO,OAAO,SAAS;AAAA,MACrB,QAAQ;AAAA,MACR,QAAQ;AAAA,MACR,UAAU;AAAA,MACV,UAAU;AAAA,MACV,gBAAgB,KAAK;AAAA,IACvB,CAAC;AACD,SAAK,gBAAgB;AACrB,SAAK,iBAAiB,KAAK;AAC3B;AAAA,EACF;AACA,SAAO,KAAK,cAAc,CAAC;AAC3B,OAAK,UAAU;AACf,OAAK,mBAAmB,OAAO,KAAK;AACpC,MAAI,sBAAsB;AAC1B,MAAI,kBAAkB,OAAO;AAC7B,MAAI,OAAO,qBAAqB;AAC9B,sBAAkB;AAAA,EACpB;AACA,MAAI,OAAO,GAAG;AACZ,QAAI,UAAU,gBAAgB,CAAC,aAAa,KAAK,sBAAsB,KAAK,oBAAoB,OAAO,iBAAiB,OAAO,aAAa,IAAI,OAAO,gBAAgB,OAAO,cAAc,CAAC,KAAK,OAAO,kBAAkB,UAAU,OAAO,OAAO,SAAS,OAAO,iBAAiB,IAAI,OAAO,gBAAgB,OAAO,cAAc,CAAC,IAAI,OAAO,OAAO,eAAe,KAAK,OAAO,OAAO,eAAe,OAAO,aAAa,IAAI;AAC9Z,aAAO,QAAQ;AAAA,QACb,WAAW;AAAA,QACX,cAAc;AAAA,QACd,kBAAkB;AAAA,MACpB,CAAC;AAAA,IACH;AACA,QAAI,KAAK,mBAAmB,OAAO,aAAa,GAAG;AACjD,4BAAsB;AACtB,UAAI,OAAO,YAAY;AACrB,aAAK,mBAAmB,OAAO,aAAa,IAAI,KAAK,CAAC,OAAO,aAAa,IAAI,KAAK,iBAAiB,SAAS;AAAA,MAC/G;AAAA,IACF;AAAA,EACF,WAAW,OAAO,GAAG;AACnB,QAAI,UAAU,gBAAgB,CAAC,aAAa,KAAK,sBAAsB,KAAK,oBAAoB,OAAO,iBAAiB,OAAO,aAAa,IAAI,OAAO,gBAAgB,OAAO,gBAAgB,SAAS,CAAC,IAAI,OAAO,OAAO,gBAAgB,OAAO,kBAAkB,UAAU,OAAO,OAAO,SAAS,OAAO,iBAAiB,IAAI,OAAO,gBAAgB,OAAO,gBAAgB,SAAS,CAAC,IAAI,OAAO,OAAO,eAAe,KAAK,OAAO,aAAa,IAAI;AACpb,aAAO,QAAQ;AAAA,QACb,WAAW;AAAA,QACX,cAAc;AAAA,QACd,kBAAkB,OAAO,OAAO,UAAU,OAAO,kBAAkB,SAAS,OAAO,qBAAqB,IAAI,KAAK,KAAK,WAAW,OAAO,eAAe,EAAE,CAAC;AAAA,MAC5J,CAAC;AAAA,IACH;AACA,QAAI,KAAK,mBAAmB,OAAO,aAAa,GAAG;AACjD,4BAAsB;AACtB,UAAI,OAAO,YAAY;AACrB,aAAK,mBAAmB,OAAO,aAAa,IAAI,KAAK,OAAO,aAAa,IAAI,KAAK,iBAAiB,SAAS;AAAA,MAC9G;AAAA,IACF;AAAA,EACF;AACA,MAAI,qBAAqB;AACvB,MAAE,0BAA0B;AAAA,EAC9B;AAGA,MAAI,CAAC,OAAO,kBAAkB,OAAO,mBAAmB,UAAU,KAAK,mBAAmB,KAAK,gBAAgB;AAC7G,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AACA,MAAI,CAAC,OAAO,kBAAkB,OAAO,mBAAmB,UAAU,KAAK,mBAAmB,KAAK,gBAAgB;AAC7G,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AACA,MAAI,CAAC,OAAO,kBAAkB,CAAC,OAAO,gBAAgB;AACpD,SAAK,mBAAmB,KAAK;AAAA,EAC/B;AAGA,MAAI,OAAO,YAAY,GAAG;AACxB,QAAI,KAAK,IAAI,IAAI,IAAI,OAAO,aAAa,KAAK,oBAAoB;AAChE,UAAI,CAAC,KAAK,oBAAoB;AAC5B,aAAK,qBAAqB;AAC1B,gBAAQ,SAAS,QAAQ;AACzB,gBAAQ,SAAS,QAAQ;AACzB,aAAK,mBAAmB,KAAK;AAC7B,gBAAQ,OAAO,OAAO,aAAa,IAAI,QAAQ,WAAW,QAAQ,SAAS,QAAQ,WAAW,QAAQ;AACtG;AAAA,MACF;AAAA,IACF,OAAO;AACL,WAAK,mBAAmB,KAAK;AAC7B;AAAA,IACF;AAAA,EACF;AACA,MAAI,CAAC,OAAO,gBAAgB,OAAO,QAAS;AAG5C,MAAI,OAAO,YAAY,OAAO,SAAS,WAAW,OAAO,YAAY,OAAO,qBAAqB;AAC/F,WAAO,kBAAkB;AACzB,WAAO,oBAAoB;AAAA,EAC7B;AACA,MAAI,OAAO,YAAY,OAAO,SAAS,WAAW,OAAO,UAAU;AACjE,WAAO,SAAS,YAAY;AAAA,EAC9B;AAEA,SAAO,eAAe,KAAK,gBAAgB;AAE3C,SAAO,aAAa,KAAK,gBAAgB;AAC3C;AACA,SAAS,WAAW,OAAO;AACzB,QAAM,SAAS;AACf,QAAM,OAAO,OAAO;AACpB,MAAI,IAAI;AACR,MAAI,EAAE,cAAe,KAAI,EAAE;AAC3B,MAAI;AACJ,QAAM,eAAe,EAAE,SAAS,cAAc,EAAE,SAAS;AACzD,MAAI,CAAC,cAAc;AACjB,QAAI,KAAK,YAAY,KAAM;AAC3B,QAAI,EAAE,cAAc,KAAK,UAAW;AACpC,kBAAc;AAAA,EAChB,OAAO;AACL,kBAAc,CAAC,GAAG,EAAE,cAAc,EAAE,KAAK,OAAK,EAAE,eAAe,KAAK,OAAO;AAC3E,QAAI,CAAC,eAAe,YAAY,eAAe,KAAK,QAAS;AAAA,EAC/D;AACA,MAAI,CAAC,iBAAiB,cAAc,gBAAgB,aAAa,EAAE,SAAS,EAAE,IAAI,GAAG;AACnF,UAAM,UAAU,CAAC,iBAAiB,aAAa,EAAE,SAAS,EAAE,IAAI,MAAM,OAAO,QAAQ,YAAY,OAAO,QAAQ;AAChH,QAAI,CAAC,SAAS;AACZ;AAAA,IACF;AAAA,EACF;AACA,OAAK,YAAY;AACjB,OAAK,UAAU;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA,cAAc;AAAA,IACd;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,QAAS;AACd,MAAI,CAAC,OAAO,iBAAiB,EAAE,gBAAgB,QAAS;AACxD,MAAI,KAAK,qBAAqB;AAC5B,WAAO,KAAK,YAAY,CAAC;AAAA,EAC3B;AACA,OAAK,sBAAsB;AAC3B,MAAI,CAAC,KAAK,WAAW;AACnB,QAAI,KAAK,WAAW,OAAO,YAAY;AACrC,aAAO,cAAc,KAAK;AAAA,IAC5B;AACA,SAAK,UAAU;AACf,SAAK,cAAc;AACnB;AAAA,EACF;AAGA,MAAI,OAAO,cAAc,KAAK,WAAW,KAAK,cAAc,OAAO,mBAAmB,QAAQ,OAAO,mBAAmB,OAAO;AAC7H,WAAO,cAAc,KAAK;AAAA,EAC5B;AAGA,QAAM,eAAe,IAAI;AACzB,QAAM,WAAW,eAAe,KAAK;AAGrC,MAAI,OAAO,YAAY;AACrB,UAAM,WAAW,EAAE,QAAQ,EAAE,gBAAgB,EAAE,aAAa;AAC5D,WAAO,mBAAmB,YAAY,SAAS,CAAC,KAAK,EAAE,QAAQ,QAAQ;AACvE,WAAO,KAAK,aAAa,CAAC;AAC1B,QAAI,WAAW,OAAO,eAAe,KAAK,gBAAgB,KAAK;AAC7D,aAAO,KAAK,yBAAyB,CAAC;AAAA,IACxC;AAAA,EACF;AACA,OAAK,gBAAgB,IAAI;AACzB,WAAS,MAAM;AACb,QAAI,CAAC,OAAO,UAAW,QAAO,aAAa;AAAA,EAC7C,CAAC;AACD,MAAI,CAAC,KAAK,aAAa,CAAC,KAAK,WAAW,CAAC,OAAO,kBAAkB,QAAQ,SAAS,KAAK,CAAC,KAAK,iBAAiB,KAAK,qBAAqB,KAAK,kBAAkB,CAAC,KAAK,eAAe;AACnL,SAAK,YAAY;AACjB,SAAK,UAAU;AACf,SAAK,cAAc;AACnB;AAAA,EACF;AACA,OAAK,YAAY;AACjB,OAAK,UAAU;AACf,OAAK,cAAc;AACnB,MAAI;AACJ,MAAI,OAAO,cAAc;AACvB,iBAAa,MAAM,OAAO,YAAY,CAAC,OAAO;AAAA,EAChD,OAAO;AACL,iBAAa,CAAC,KAAK;AAAA,EACrB;AACA,MAAI,OAAO,SAAS;AAClB;AAAA,EACF;AACA,MAAI,OAAO,YAAY,OAAO,SAAS,SAAS;AAC9C,WAAO,SAAS,WAAW;AAAA,MACzB;AAAA,IACF,CAAC;AACD;AAAA,EACF;AAGA,QAAM,cAAc,cAAc,CAAC,OAAO,aAAa,KAAK,CAAC,OAAO,OAAO;AAC3E,MAAI,YAAY;AAChB,MAAI,YAAY,OAAO,gBAAgB,CAAC;AACxC,WAAS,IAAI,GAAG,IAAI,WAAW,QAAQ,KAAK,IAAI,OAAO,qBAAqB,IAAI,OAAO,gBAAgB;AACrG,UAAMc,aAAY,IAAI,OAAO,qBAAqB,IAAI,IAAI,OAAO;AACjE,QAAI,OAAO,WAAW,IAAIA,UAAS,MAAM,aAAa;AACpD,UAAI,eAAe,cAAc,WAAW,CAAC,KAAK,aAAa,WAAW,IAAIA,UAAS,GAAG;AACxF,oBAAY;AACZ,oBAAY,WAAW,IAAIA,UAAS,IAAI,WAAW,CAAC;AAAA,MACtD;AAAA,IACF,WAAW,eAAe,cAAc,WAAW,CAAC,GAAG;AACrD,kBAAY;AACZ,kBAAY,WAAW,WAAW,SAAS,CAAC,IAAI,WAAW,WAAW,SAAS,CAAC;AAAA,IAClF;AAAA,EACF;AACA,MAAI,mBAAmB;AACvB,MAAI,kBAAkB;AACtB,MAAI,OAAO,QAAQ;AACjB,QAAI,OAAO,aAAa;AACtB,wBAAkB,OAAO,WAAW,OAAO,QAAQ,WAAW,OAAO,UAAU,OAAO,QAAQ,OAAO,SAAS,IAAI,OAAO,OAAO,SAAS;AAAA,IAC3I,WAAW,OAAO,OAAO;AACvB,yBAAmB;AAAA,IACrB;AAAA,EACF;AAEA,QAAM,SAAS,aAAa,WAAW,SAAS,KAAK;AACrD,QAAM,YAAY,YAAY,OAAO,qBAAqB,IAAI,IAAI,OAAO;AACzE,MAAI,WAAW,OAAO,cAAc;AAElC,QAAI,CAAC,OAAO,YAAY;AACtB,aAAO,QAAQ,OAAO,WAAW;AACjC;AAAA,IACF;AACA,QAAI,OAAO,mBAAmB,QAAQ;AACpC,UAAI,SAAS,OAAO,gBAAiB,QAAO,QAAQ,OAAO,UAAU,OAAO,QAAQ,mBAAmB,YAAY,SAAS;AAAA,UAAO,QAAO,QAAQ,SAAS;AAAA,IAC7J;AACA,QAAI,OAAO,mBAAmB,QAAQ;AACpC,UAAI,QAAQ,IAAI,OAAO,iBAAiB;AACtC,eAAO,QAAQ,YAAY,SAAS;AAAA,MACtC,WAAW,oBAAoB,QAAQ,QAAQ,KAAK,KAAK,IAAI,KAAK,IAAI,OAAO,iBAAiB;AAC5F,eAAO,QAAQ,eAAe;AAAA,MAChC,OAAO;AACL,eAAO,QAAQ,SAAS;AAAA,MAC1B;AAAA,IACF;AAAA,EACF,OAAO;AAEL,QAAI,CAAC,OAAO,aAAa;AACvB,aAAO,QAAQ,OAAO,WAAW;AACjC;AAAA,IACF;AACA,UAAM,oBAAoB,OAAO,eAAe,EAAE,WAAW,OAAO,WAAW,UAAU,EAAE,WAAW,OAAO,WAAW;AACxH,QAAI,CAAC,mBAAmB;AACtB,UAAI,OAAO,mBAAmB,QAAQ;AACpC,eAAO,QAAQ,qBAAqB,OAAO,mBAAmB,YAAY,SAAS;AAAA,MACrF;AACA,UAAI,OAAO,mBAAmB,QAAQ;AACpC,eAAO,QAAQ,oBAAoB,OAAO,kBAAkB,SAAS;AAAA,MACvE;AAAA,IACF,WAAW,EAAE,WAAW,OAAO,WAAW,QAAQ;AAChD,aAAO,QAAQ,YAAY,SAAS;AAAA,IACtC,OAAO;AACL,aAAO,QAAQ,SAAS;AAAA,IAC1B;AAAA,EACF;AACF;AACA,SAAS,WAAW;AAClB,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,MAAM,GAAG,gBAAgB,EAAG;AAGhC,MAAI,OAAO,aAAa;AACtB,WAAO,cAAc;AAAA,EACvB;AAGA,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,YAAY,OAAO,WAAW,OAAO,OAAO,QAAQ;AAG1D,SAAO,iBAAiB;AACxB,SAAO,iBAAiB;AACxB,SAAO,WAAW;AAClB,SAAO,aAAa;AACpB,SAAO,oBAAoB;AAC3B,QAAM,gBAAgB,aAAa,OAAO;AAC1C,OAAK,OAAO,kBAAkB,UAAU,OAAO,gBAAgB,MAAM,OAAO,SAAS,CAAC,OAAO,eAAe,CAAC,OAAO,OAAO,kBAAkB,CAAC,eAAe;AAC3J,WAAO,QAAQ,OAAO,OAAO,SAAS,GAAG,GAAG,OAAO,IAAI;AAAA,EACzD,OAAO;AACL,QAAI,OAAO,OAAO,QAAQ,CAAC,WAAW;AACpC,aAAO,YAAY,OAAO,WAAW,GAAG,OAAO,IAAI;AAAA,IACrD,OAAO;AACL,aAAO,QAAQ,OAAO,aAAa,GAAG,OAAO,IAAI;AAAA,IACnD;AAAA,EACF;AACA,MAAI,OAAO,YAAY,OAAO,SAAS,WAAW,OAAO,SAAS,QAAQ;AACxE,iBAAa,OAAO,SAAS,aAAa;AAC1C,WAAO,SAAS,gBAAgB,WAAW,MAAM;AAC/C,UAAI,OAAO,YAAY,OAAO,SAAS,WAAW,OAAO,SAAS,QAAQ;AACxE,eAAO,SAAS,OAAO;AAAA,MACzB;AAAA,IACF,GAAG,GAAG;AAAA,EACR;AAEA,SAAO,iBAAiB;AACxB,SAAO,iBAAiB;AACxB,MAAI,OAAO,OAAO,iBAAiB,aAAa,OAAO,UAAU;AAC/D,WAAO,cAAc;AAAA,EACvB;AACF;AACA,SAAS,QAAQ,GAAG;AAClB,QAAM,SAAS;AACf,MAAI,CAAC,OAAO,QAAS;AACrB,MAAI,CAAC,OAAO,YAAY;AACtB,QAAI,OAAO,OAAO,cAAe,GAAE,eAAe;AAClD,QAAI,OAAO,OAAO,4BAA4B,OAAO,WAAW;AAC9D,QAAE,gBAAgB;AAClB,QAAE,yBAAyB;AAAA,IAC7B;AAAA,EACF;AACF;AACA,SAAS,WAAW;AAClB,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,QAAS;AACd,SAAO,oBAAoB,OAAO;AAClC,MAAI,OAAO,aAAa,GAAG;AACzB,WAAO,YAAY,CAAC,UAAU;AAAA,EAChC,OAAO;AACL,WAAO,YAAY,CAAC,UAAU;AAAA,EAChC;AAEA,MAAI,OAAO,cAAc,EAAG,QAAO,YAAY;AAC/C,SAAO,kBAAkB;AACzB,SAAO,oBAAoB;AAC3B,MAAI;AACJ,QAAM,iBAAiB,OAAO,aAAa,IAAI,OAAO,aAAa;AACnE,MAAI,mBAAmB,GAAG;AACxB,kBAAc;AAAA,EAChB,OAAO;AACL,mBAAe,OAAO,YAAY,OAAO,aAAa,KAAK;AAAA,EAC7D;AACA,MAAI,gBAAgB,OAAO,UAAU;AACnC,WAAO,eAAe,eAAe,CAAC,OAAO,YAAY,OAAO,SAAS;AAAA,EAC3E;AACA,SAAO,KAAK,gBAAgB,OAAO,WAAW,KAAK;AACrD;AACA,SAAS,OAAO,GAAG;AACjB,QAAM,SAAS;AACf,uBAAqB,QAAQ,EAAE,MAAM;AACrC,MAAI,OAAO,OAAO,WAAW,OAAO,OAAO,kBAAkB,UAAU,CAAC,OAAO,OAAO,YAAY;AAChG;AAAA,EACF;AACA,SAAO,OAAO;AAChB;AACA,SAAS,uBAAuB;AAC9B,QAAM,SAAS;AACf,MAAI,OAAO,8BAA+B;AAC1C,SAAO,gCAAgC;AACvC,MAAI,OAAO,OAAO,qBAAqB;AACrC,WAAO,GAAG,MAAM,cAAc;AAAA,EAChC;AACF;AACA,IAAM,SAAS,CAAC,QAAQ,WAAW;AACjC,QAAMd,YAAW,YAAY;AAC7B,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAM,UAAU,CAAC,CAAC,OAAO;AACzB,QAAM,YAAY,WAAW,OAAO,qBAAqB;AACzD,QAAM,eAAe;AACrB,MAAI,CAAC,MAAM,OAAO,OAAO,SAAU;AAGnC,EAAAA,UAAS,SAAS,EAAE,cAAc,OAAO,sBAAsB;AAAA,IAC7D,SAAS;AAAA,IACT;AAAA,EACF,CAAC;AACD,KAAG,SAAS,EAAE,cAAc,OAAO,cAAc;AAAA,IAC/C,SAAS;AAAA,EACX,CAAC;AACD,KAAG,SAAS,EAAE,eAAe,OAAO,cAAc;AAAA,IAChD,SAAS;AAAA,EACX,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,aAAa,OAAO,aAAa;AAAA,IACnD,SAAS;AAAA,IACT;AAAA,EACF,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,eAAe,OAAO,aAAa;AAAA,IACrD,SAAS;AAAA,IACT;AAAA,EACF,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,YAAY,OAAO,YAAY;AAAA,IACjD,SAAS;AAAA,EACX,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,aAAa,OAAO,YAAY;AAAA,IAClD,SAAS;AAAA,EACX,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,iBAAiB,OAAO,YAAY;AAAA,IACtD,SAAS;AAAA,EACX,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,eAAe,OAAO,YAAY;AAAA,IACpD,SAAS;AAAA,EACX,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,cAAc,OAAO,YAAY;AAAA,IACnD,SAAS;AAAA,EACX,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,gBAAgB,OAAO,YAAY;AAAA,IACrD,SAAS;AAAA,EACX,CAAC;AACD,EAAAA,UAAS,SAAS,EAAE,eAAe,OAAO,YAAY;AAAA,IACpD,SAAS;AAAA,EACX,CAAC;AAGD,MAAI,OAAO,iBAAiB,OAAO,0BAA0B;AAC3D,OAAG,SAAS,EAAE,SAAS,OAAO,SAAS,IAAI;AAAA,EAC7C;AACA,MAAI,OAAO,SAAS;AAClB,cAAU,SAAS,EAAE,UAAU,OAAO,QAAQ;AAAA,EAChD;AAGA,MAAI,OAAO,sBAAsB;AAC/B,WAAO,YAAY,EAAE,OAAO,OAAO,OAAO,UAAU,4CAA4C,yBAAyB,UAAU,IAAI;AAAA,EACzI,OAAO;AACL,WAAO,YAAY,EAAE,kBAAkB,UAAU,IAAI;AAAA,EACvD;AAGA,KAAG,SAAS,EAAE,QAAQ,OAAO,QAAQ;AAAA,IACnC,SAAS;AAAA,EACX,CAAC;AACH;AACA,SAAS,eAAe;AACtB,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,SAAO,eAAe,aAAa,KAAK,MAAM;AAC9C,SAAO,cAAc,YAAY,KAAK,MAAM;AAC5C,SAAO,aAAa,WAAW,KAAK,MAAM;AAC1C,SAAO,uBAAuB,qBAAqB,KAAK,MAAM;AAC9D,MAAI,OAAO,SAAS;AAClB,WAAO,WAAW,SAAS,KAAK,MAAM;AAAA,EACxC;AACA,SAAO,UAAU,QAAQ,KAAK,MAAM;AACpC,SAAO,SAAS,OAAO,KAAK,MAAM;AAClC,SAAO,QAAQ,IAAI;AACrB;AACA,SAAS,eAAe;AACtB,QAAM,SAAS;AACf,SAAO,QAAQ,KAAK;AACtB;AACA,IAAI,WAAW;AAAA,EACb;AAAA,EACA;AACF;AACA,IAAM,gBAAgB,CAAC,QAAQ,WAAW;AACxC,SAAO,OAAO,QAAQ,OAAO,QAAQ,OAAO,KAAK,OAAO;AAC1D;AACA,SAAS,gBAAgB;AACvB,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AACJ,QAAMe,eAAc,OAAO;AAC3B,MAAI,CAACA,gBAAeA,gBAAe,OAAO,KAAKA,YAAW,EAAE,WAAW,EAAG;AAC1E,QAAMf,YAAW,YAAY;AAG7B,QAAM,kBAAkB,OAAO,oBAAoB,YAAY,CAAC,OAAO,kBAAkB,OAAO,kBAAkB;AAClH,QAAM,sBAAsB,CAAC,UAAU,WAAW,EAAE,SAAS,OAAO,eAAe,KAAK,CAAC,OAAO,kBAAkB,OAAO,KAAKA,UAAS,cAAc,OAAO,eAAe;AAC3K,QAAM,aAAa,OAAO,cAAce,cAAa,iBAAiB,mBAAmB;AACzF,MAAI,CAAC,cAAc,OAAO,sBAAsB,WAAY;AAC5D,QAAM,uBAAuB,cAAcA,eAAcA,aAAY,UAAU,IAAI;AACnF,QAAM,mBAAmB,wBAAwB,OAAO;AACxD,QAAM,cAAc,cAAc,QAAQ,MAAM;AAChD,QAAM,aAAa,cAAc,QAAQ,gBAAgB;AACzD,QAAM,gBAAgB,OAAO,OAAO;AACpC,QAAM,eAAe,iBAAiB;AACtC,QAAM,aAAa,OAAO;AAC1B,MAAI,eAAe,CAAC,YAAY;AAC9B,OAAG,UAAU,OAAO,GAAG,OAAO,sBAAsB,QAAQ,GAAG,OAAO,sBAAsB,aAAa;AACzG,WAAO,qBAAqB;AAAA,EAC9B,WAAW,CAAC,eAAe,YAAY;AACrC,OAAG,UAAU,IAAI,GAAG,OAAO,sBAAsB,MAAM;AACvD,QAAI,iBAAiB,KAAK,QAAQ,iBAAiB,KAAK,SAAS,YAAY,CAAC,iBAAiB,KAAK,QAAQ,OAAO,KAAK,SAAS,UAAU;AACzI,SAAG,UAAU,IAAI,GAAG,OAAO,sBAAsB,aAAa;AAAA,IAChE;AACA,WAAO,qBAAqB;AAAA,EAC9B;AACA,MAAI,iBAAiB,CAAC,cAAc;AAClC,WAAO,gBAAgB;AAAA,EACzB,WAAW,CAAC,iBAAiB,cAAc;AACzC,WAAO,cAAc;AAAA,EACvB;AAGA,GAAC,cAAc,cAAc,WAAW,EAAE,QAAQ,UAAQ;AACxD,QAAI,OAAO,iBAAiB,IAAI,MAAM,YAAa;AACnD,UAAM,mBAAmB,OAAO,IAAI,KAAK,OAAO,IAAI,EAAE;AACtD,UAAM,kBAAkB,iBAAiB,IAAI,KAAK,iBAAiB,IAAI,EAAE;AACzE,QAAI,oBAAoB,CAAC,iBAAiB;AACxC,aAAO,IAAI,EAAE,QAAQ;AAAA,IACvB;AACA,QAAI,CAAC,oBAAoB,iBAAiB;AACxC,aAAO,IAAI,EAAE,OAAO;AAAA,IACtB;AAAA,EACF,CAAC;AACD,QAAM,mBAAmB,iBAAiB,aAAa,iBAAiB,cAAc,OAAO;AAC7F,QAAM,cAAc,OAAO,SAAS,iBAAiB,kBAAkB,OAAO,iBAAiB;AAC/F,QAAM,UAAU,OAAO;AACvB,MAAI,oBAAoB,aAAa;AACnC,WAAO,gBAAgB;AAAA,EACzB;AACA,SAAO,OAAO,QAAQ,gBAAgB;AACtC,QAAM,YAAY,OAAO,OAAO;AAChC,QAAM,UAAU,OAAO,OAAO;AAC9B,SAAO,OAAO,QAAQ;AAAA,IACpB,gBAAgB,OAAO,OAAO;AAAA,IAC9B,gBAAgB,OAAO,OAAO;AAAA,IAC9B,gBAAgB,OAAO,OAAO;AAAA,EAChC,CAAC;AACD,MAAI,cAAc,CAAC,WAAW;AAC5B,WAAO,QAAQ;AAAA,EACjB,WAAW,CAAC,cAAc,WAAW;AACnC,WAAO,OAAO;AAAA,EAChB;AACA,SAAO,oBAAoB;AAC3B,SAAO,KAAK,qBAAqB,gBAAgB;AACjD,MAAI,aAAa;AACf,QAAI,aAAa;AACf,aAAO,YAAY;AACnB,aAAO,WAAW,SAAS;AAC3B,aAAO,aAAa;AAAA,IACtB,WAAW,CAAC,WAAW,SAAS;AAC9B,aAAO,WAAW,SAAS;AAC3B,aAAO,aAAa;AAAA,IACtB,WAAW,WAAW,CAAC,SAAS;AAC9B,aAAO,YAAY;AAAA,IACrB;AAAA,EACF;AACA,SAAO,KAAK,cAAc,gBAAgB;AAC5C;AACA,SAAS,cAAcA,cAAa,MAAM,aAAa;AACrD,MAAI,SAAS,QAAQ;AACnB,WAAO;AAAA,EACT;AACA,MAAI,CAACA,gBAAe,SAAS,eAAe,CAAC,YAAa,QAAO;AACjE,MAAI,aAAa;AACjB,QAAMhB,UAAS,UAAU;AACzB,QAAM,gBAAgB,SAAS,WAAWA,QAAO,cAAc,YAAY;AAC3E,QAAM,SAAS,OAAO,KAAKgB,YAAW,EAAE,IAAI,WAAS;AACnD,QAAI,OAAO,UAAU,YAAY,MAAM,QAAQ,GAAG,MAAM,GAAG;AACzD,YAAM,WAAW,WAAW,MAAM,OAAO,CAAC,CAAC;AAC3C,YAAM,QAAQ,gBAAgB;AAC9B,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,OAAO;AAAA,MACP;AAAA,IACF;AAAA,EACF,CAAC;AACD,SAAO,KAAK,CAAC,GAAG,MAAM,SAAS,EAAE,OAAO,EAAE,IAAI,SAAS,EAAE,OAAO,EAAE,CAAC;AACnE,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACzC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI,OAAO,CAAC;AACZ,QAAI,SAAS,UAAU;AACrB,UAAIhB,QAAO,WAAW,eAAe,KAAK,KAAK,EAAE,SAAS;AACxD,qBAAa;AAAA,MACf;AAAA,IACF,WAAW,SAAS,YAAY,aAAa;AAC3C,mBAAa;AAAA,IACf;AAAA,EACF;AACA,SAAO,cAAc;AACvB;AACA,IAAI,cAAc;AAAA,EAChB;AAAA,EACA;AACF;AACA,SAAS,eAAe,SAAS,QAAQ;AACvC,QAAM,gBAAgB,CAAC;AACvB,UAAQ,QAAQ,UAAQ;AACtB,QAAI,OAAO,SAAS,UAAU;AAC5B,aAAO,KAAK,IAAI,EAAE,QAAQ,gBAAc;AACtC,YAAI,KAAK,UAAU,GAAG;AACpB,wBAAc,KAAK,SAAS,UAAU;AAAA,QACxC;AAAA,MACF,CAAC;AAAA,IACH,WAAW,OAAO,SAAS,UAAU;AACnC,oBAAc,KAAK,SAAS,IAAI;AAAA,IAClC;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,aAAa;AACpB,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IAAI;AAEJ,QAAM,WAAW,eAAe,CAAC,eAAe,OAAO,WAAW;AAAA,IAChE,aAAa,OAAO,OAAO,YAAY,OAAO,SAAS;AAAA,EACzD,GAAG;AAAA,IACD,cAAc,OAAO;AAAA,EACvB,GAAG;AAAA,IACD,OAAO;AAAA,EACT,GAAG;AAAA,IACD,QAAQ,OAAO,QAAQ,OAAO,KAAK,OAAO;AAAA,EAC5C,GAAG;AAAA,IACD,eAAe,OAAO,QAAQ,OAAO,KAAK,OAAO,KAAK,OAAO,KAAK,SAAS;AAAA,EAC7E,GAAG;AAAA,IACD,WAAW,OAAO;AAAA,EACpB,GAAG;AAAA,IACD,OAAO,OAAO;AAAA,EAChB,GAAG;AAAA,IACD,YAAY,OAAO;AAAA,EACrB,GAAG;AAAA,IACD,YAAY,OAAO,WAAW,OAAO;AAAA,EACvC,GAAG;AAAA,IACD,kBAAkB,OAAO;AAAA,EAC3B,CAAC,GAAG,OAAO,sBAAsB;AACjC,aAAW,KAAK,GAAG,QAAQ;AAC3B,KAAG,UAAU,IAAI,GAAG,UAAU;AAC9B,SAAO,qBAAqB;AAC9B;AACA,SAAS,gBAAgB;AACvB,QAAM,SAAS;AACf,QAAM;AAAA,IACJ;AAAA,IACA;AAAA,EACF,IAAI;AACJ,MAAI,CAAC,MAAM,OAAO,OAAO,SAAU;AACnC,KAAG,UAAU,OAAO,GAAG,UAAU;AACjC,SAAO,qBAAqB;AAC9B;AACA,IAAI,UAAU;AAAA,EACZ;AAAA,EACA;AACF;AACA,SAAS,gBAAgB;AACvB,QAAM,SAAS;AACf,QAAM;AAAA,IACJ,UAAU;AAAA,IACV;AAAA,EACF,IAAI;AACJ,QAAM;AAAA,IACJ;AAAA,EACF,IAAI;AACJ,MAAI,oBAAoB;AACtB,UAAM,iBAAiB,OAAO,OAAO,SAAS;AAC9C,UAAM,qBAAqB,OAAO,WAAW,cAAc,IAAI,OAAO,gBAAgB,cAAc,IAAI,qBAAqB;AAC7H,WAAO,WAAW,OAAO,OAAO;AAAA,EAClC,OAAO;AACL,WAAO,WAAW,OAAO,SAAS,WAAW;AAAA,EAC/C;AACA,MAAI,OAAO,mBAAmB,MAAM;AAClC,WAAO,iBAAiB,CAAC,OAAO;AAAA,EAClC;AACA,MAAI,OAAO,mBAAmB,MAAM;AAClC,WAAO,iBAAiB,CAAC,OAAO;AAAA,EAClC;AACA,MAAI,aAAa,cAAc,OAAO,UAAU;AAC9C,WAAO,QAAQ;AAAA,EACjB;AACA,MAAI,cAAc,OAAO,UAAU;AACjC,WAAO,KAAK,OAAO,WAAW,SAAS,QAAQ;AAAA,EACjD;AACF;AACA,IAAI,kBAAkB;AAAA,EACpB;AACF;AACA,IAAI,WAAW;AAAA,EACb,MAAM;AAAA,EACN,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,mBAAmB;AAAA,EACnB,cAAc;AAAA,EACd,OAAO;AAAA,EACP,SAAS;AAAA,EACT,sBAAsB;AAAA,EACtB,gBAAgB;AAAA,EAChB,QAAQ;AAAA,EACR,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,SAAS;AAAA,EACT,mBAAmB;AAAA;AAAA,EAEnB,OAAO;AAAA,EACP,QAAQ;AAAA;AAAA,EAER,gCAAgC;AAAA;AAAA,EAEhC,WAAW;AAAA,EACX,KAAK;AAAA;AAAA,EAEL,oBAAoB;AAAA,EACpB,oBAAoB;AAAA;AAAA,EAEpB,YAAY;AAAA;AAAA,EAEZ,gBAAgB;AAAA;AAAA,EAEhB,kBAAkB;AAAA;AAAA,EAElB,QAAQ;AAAA;AAAA;AAAA,EAIR,aAAa;AAAA,EACb,iBAAiB;AAAA;AAAA,EAEjB,cAAc;AAAA,EACd,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,oBAAoB;AAAA,EACpB,oBAAoB;AAAA,EACpB,gBAAgB;AAAA,EAChB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA;AAAA,EAEpB,mBAAmB;AAAA;AAAA,EAEnB,qBAAqB;AAAA,EACrB,0BAA0B;AAAA;AAAA,EAE1B,eAAe;AAAA;AAAA,EAEf,cAAc;AAAA;AAAA,EAEd,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,eAAe;AAAA,EACf,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,cAAc;AAAA,EACd,cAAc;AAAA,EACd,gBAAgB;AAAA,EAChB,WAAW;AAAA,EACX,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA;AAAA,EAErB,mBAAmB;AAAA;AAAA,EAEnB,YAAY;AAAA,EACZ,iBAAiB;AAAA;AAAA,EAEjB,qBAAqB;AAAA;AAAA,EAErB,YAAY;AAAA;AAAA,EAEZ,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA;AAAA,EAErB,MAAM;AAAA,EACN,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA;AAAA,EAErB,QAAQ;AAAA;AAAA,EAER,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,cAAc;AAAA;AAAA,EAEd,WAAW;AAAA,EACX,gBAAgB;AAAA,EAChB,mBAAmB;AAAA;AAAA,EAEnB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA;AAAA,EAEzB,wBAAwB;AAAA;AAAA,EAExB,YAAY;AAAA,EACZ,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,gBAAgB;AAAA,EAChB,gBAAgB;AAAA,EAChB,cAAc;AAAA,EACd,oBAAoB;AAAA,EACpB,qBAAqB;AAAA;AAAA,EAErB,oBAAoB;AAAA;AAAA,EAEpB,cAAc;AAChB;AACA,SAAS,mBAAmB,QAAQ,kBAAkB;AACpD,SAAO,SAAS,aAAa,KAAK;AAChC,QAAI,QAAQ,QAAQ;AAClB,YAAM,CAAC;AAAA,IACT;AACA,UAAM,kBAAkB,OAAO,KAAK,GAAG,EAAE,CAAC;AAC1C,UAAM,eAAe,IAAI,eAAe;AACxC,QAAI,OAAO,iBAAiB,YAAY,iBAAiB,MAAM;AAC7D,aAAO,kBAAkB,GAAG;AAC5B;AAAA,IACF;AACA,QAAI,OAAO,eAAe,MAAM,MAAM;AACpC,aAAO,eAAe,IAAI;AAAA,QACxB,SAAS;AAAA,MACX;AAAA,IACF;AACA,QAAI,oBAAoB,gBAAgB,OAAO,eAAe,KAAK,OAAO,eAAe,EAAE,WAAW,CAAC,OAAO,eAAe,EAAE,UAAU,CAAC,OAAO,eAAe,EAAE,QAAQ;AACxK,aAAO,eAAe,EAAE,OAAO;AAAA,IACjC;AACA,QAAI,CAAC,cAAc,WAAW,EAAE,QAAQ,eAAe,KAAK,KAAK,OAAO,eAAe,KAAK,OAAO,eAAe,EAAE,WAAW,CAAC,OAAO,eAAe,EAAE,IAAI;AAC1J,aAAO,eAAe,EAAE,OAAO;AAAA,IACjC;AACA,QAAI,EAAE,mBAAmB,UAAU,aAAa,eAAe;AAC7D,aAAO,kBAAkB,GAAG;AAC5B;AAAA,IACF;AACA,QAAI,OAAO,OAAO,eAAe,MAAM,YAAY,EAAE,aAAa,OAAO,eAAe,IAAI;AAC1F,aAAO,eAAe,EAAE,UAAU;AAAA,IACpC;AACA,QAAI,CAAC,OAAO,eAAe,EAAG,QAAO,eAAe,IAAI;AAAA,MACtD,SAAS;AAAA,IACX;AACA,WAAO,kBAAkB,GAAG;AAAA,EAC9B;AACF;AAGA,IAAM,aAAa;AAAA,EACjB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,QAAQ;AAAA,EACR;AAAA,EACA,eAAe;AAAA,EACf;AACF;AACA,IAAM,mBAAmB,CAAC;AAC1B,IAAM,SAAN,MAAM,QAAO;AAAA,EACX,cAAc;AACZ,QAAI;AACJ,QAAI;AACJ,aAAS,OAAO,UAAU,QAAQ,OAAO,IAAI,MAAM,IAAI,GAAG,OAAO,GAAG,OAAO,MAAM,QAAQ;AACvF,WAAK,IAAI,IAAI,UAAU,IAAI;AAAA,IAC7B;AACA,QAAI,KAAK,WAAW,KAAK,KAAK,CAAC,EAAE,eAAe,OAAO,UAAU,SAAS,KAAK,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,MAAM,UAAU;AACjH,eAAS,KAAK,CAAC;AAAA,IACjB,OAAO;AACL,OAAC,IAAI,MAAM,IAAI;AAAA,IACjB;AACA,QAAI,CAAC,OAAQ,UAAS,CAAC;AACvB,aAAS,OAAO,CAAC,GAAG,MAAM;AAC1B,QAAI,MAAM,CAAC,OAAO,GAAI,QAAO,KAAK;AAClC,UAAMC,YAAW,YAAY;AAC7B,QAAI,OAAO,MAAM,OAAO,OAAO,OAAO,YAAYA,UAAS,iBAAiB,OAAO,EAAE,EAAE,SAAS,GAAG;AACjG,YAAM,UAAU,CAAC;AACjB,MAAAA,UAAS,iBAAiB,OAAO,EAAE,EAAE,QAAQ,iBAAe;AAC1D,cAAM,YAAY,OAAO,CAAC,GAAG,QAAQ;AAAA,UACnC,IAAI;AAAA,QACN,CAAC;AACD,gBAAQ,KAAK,IAAI,QAAO,SAAS,CAAC;AAAA,MACpC,CAAC;AAED,aAAO;AAAA,IACT;AAGA,UAAM,SAAS;AACf,WAAO,aAAa;AACpB,WAAO,UAAU,WAAW;AAC5B,WAAO,SAAS,UAAU;AAAA,MACxB,WAAW,OAAO;AAAA,IACpB,CAAC;AACD,WAAO,UAAU,WAAW;AAC5B,WAAO,kBAAkB,CAAC;AAC1B,WAAO,qBAAqB,CAAC;AAC7B,WAAO,UAAU,CAAC,GAAG,OAAO,WAAW;AACvC,QAAI,OAAO,WAAW,MAAM,QAAQ,OAAO,OAAO,GAAG;AACnD,aAAO,QAAQ,KAAK,GAAG,OAAO,OAAO;AAAA,IACvC;AACA,UAAM,mBAAmB,CAAC;AAC1B,WAAO,QAAQ,QAAQ,SAAO;AAC5B,UAAI;AAAA,QACF;AAAA,QACA;AAAA,QACA,cAAc,mBAAmB,QAAQ,gBAAgB;AAAA,QACzD,IAAI,OAAO,GAAG,KAAK,MAAM;AAAA,QACzB,MAAM,OAAO,KAAK,KAAK,MAAM;AAAA,QAC7B,KAAK,OAAO,IAAI,KAAK,MAAM;AAAA,QAC3B,MAAM,OAAO,KAAK,KAAK,MAAM;AAAA,MAC/B,CAAC;AAAA,IACH,CAAC;AAGD,UAAM,eAAe,OAAO,CAAC,GAAG,UAAU,gBAAgB;AAG1D,WAAO,SAAS,OAAO,CAAC,GAAG,cAAc,kBAAkB,MAAM;AACjE,WAAO,iBAAiB,OAAO,CAAC,GAAG,OAAO,MAAM;AAChD,WAAO,eAAe,OAAO,CAAC,GAAG,MAAM;AAGvC,QAAI,OAAO,UAAU,OAAO,OAAO,IAAI;AACrC,aAAO,KAAK,OAAO,OAAO,EAAE,EAAE,QAAQ,eAAa;AACjD,eAAO,GAAG,WAAW,OAAO,OAAO,GAAG,SAAS,CAAC;AAAA,MAClD,CAAC;AAAA,IACH;AACA,QAAI,OAAO,UAAU,OAAO,OAAO,OAAO;AACxC,aAAO,MAAM,OAAO,OAAO,KAAK;AAAA,IAClC;AAGA,WAAO,OAAO,QAAQ;AAAA,MACpB,SAAS,OAAO,OAAO;AAAA,MACvB;AAAA;AAAA,MAEA,YAAY,CAAC;AAAA;AAAA,MAEb,QAAQ,CAAC;AAAA,MACT,YAAY,CAAC;AAAA,MACb,UAAU,CAAC;AAAA,MACX,iBAAiB,CAAC;AAAA;AAAA,MAElB,eAAe;AACb,eAAO,OAAO,OAAO,cAAc;AAAA,MACrC;AAAA,MACA,aAAa;AACX,eAAO,OAAO,OAAO,cAAc;AAAA,MACrC;AAAA;AAAA,MAEA,aAAa;AAAA,MACb,WAAW;AAAA;AAAA,MAEX,aAAa;AAAA,MACb,OAAO;AAAA;AAAA,MAEP,WAAW;AAAA,MACX,mBAAmB;AAAA,MACnB,UAAU;AAAA,MACV,UAAU;AAAA,MACV,WAAW;AAAA,MACX,wBAAwB;AAGtB,eAAO,KAAK,MAAM,KAAK,YAAY,KAAK,EAAE,IAAI,KAAK;AAAA,MACrD;AAAA;AAAA,MAEA,gBAAgB,OAAO,OAAO;AAAA,MAC9B,gBAAgB,OAAO,OAAO;AAAA;AAAA,MAE9B,iBAAiB;AAAA,QACf,WAAW;AAAA,QACX,SAAS;AAAA,QACT,qBAAqB;AAAA,QACrB,gBAAgB;AAAA,QAChB,aAAa;AAAA,QACb,kBAAkB;AAAA,QAClB,gBAAgB;AAAA,QAChB,oBAAoB;AAAA;AAAA,QAEpB,mBAAmB,OAAO,OAAO;AAAA;AAAA,QAEjC,eAAe;AAAA,QACf,cAAc;AAAA;AAAA,QAEd,YAAY,CAAC;AAAA,QACb,qBAAqB;AAAA,QACrB,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA,MACX;AAAA;AAAA,MAEA,YAAY;AAAA;AAAA,MAEZ,gBAAgB,OAAO,OAAO;AAAA,MAC9B,SAAS;AAAA,QACP,QAAQ;AAAA,QACR,QAAQ;AAAA,QACR,UAAU;AAAA,QACV,UAAU;AAAA,QACV,MAAM;AAAA,MACR;AAAA;AAAA,MAEA,cAAc,CAAC;AAAA,MACf,cAAc;AAAA,IAChB,CAAC;AACD,WAAO,KAAK,SAAS;AAGrB,QAAI,OAAO,OAAO,MAAM;AACtB,aAAO,KAAK;AAAA,IACd;AAIA,WAAO;AAAA,EACT;AAAA,EACA,kBAAkB,UAAU;AAC1B,QAAI,KAAK,aAAa,GAAG;AACvB,aAAO;AAAA,IACT;AAEA,WAAO;AAAA,MACL,SAAS;AAAA,MACT,cAAc;AAAA,MACd,kBAAkB;AAAA,MAClB,eAAe;AAAA,MACf,gBAAgB;AAAA,MAChB,gBAAgB;AAAA,MAChB,iBAAiB;AAAA,MACjB,eAAe;AAAA,IACjB,EAAE,QAAQ;AAAA,EACZ;AAAA,EACA,cAAc,SAAS;AACrB,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,gBAAgB,UAAU,IAAI,OAAO,UAAU,gBAAgB;AAC9E,UAAM,kBAAkB,aAAa,OAAO,CAAC,CAAC;AAC9C,WAAO,aAAa,OAAO,IAAI;AAAA,EACjC;AAAA,EACA,oBAAoB,OAAO;AACzB,WAAO,KAAK,cAAc,KAAK,OAAO,KAAK,aAAW,QAAQ,aAAa,yBAAyB,IAAI,MAAM,KAAK,CAAC;AAAA,EACtH;AAAA,EACA,sBAAsB,OAAO;AAC3B,QAAI,KAAK,QAAQ,KAAK,OAAO,QAAQ,KAAK,OAAO,KAAK,OAAO,GAAG;AAC9D,UAAI,KAAK,OAAO,KAAK,SAAS,UAAU;AACtC,gBAAQ,KAAK,MAAM,QAAQ,KAAK,OAAO,KAAK,IAAI;AAAA,MAClD,WAAW,KAAK,OAAO,KAAK,SAAS,OAAO;AAC1C,gBAAQ,QAAQ,KAAK,KAAK,KAAK,OAAO,SAAS,KAAK,OAAO,KAAK,IAAI;AAAA,MACtE;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,eAAe;AACb,UAAM,SAAS;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AACJ,WAAO,SAAS,gBAAgB,UAAU,IAAI,OAAO,UAAU,gBAAgB;AAAA,EACjF;AAAA,EACA,SAAS;AACP,UAAM,SAAS;AACf,QAAI,OAAO,QAAS;AACpB,WAAO,UAAU;AACjB,QAAI,OAAO,OAAO,YAAY;AAC5B,aAAO,cAAc;AAAA,IACvB;AACA,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,UAAU;AACR,UAAM,SAAS;AACf,QAAI,CAAC,OAAO,QAAS;AACrB,WAAO,UAAU;AACjB,QAAI,OAAO,OAAO,YAAY;AAC5B,aAAO,gBAAgB;AAAA,IACzB;AACA,WAAO,KAAK,SAAS;AAAA,EACvB;AAAA,EACA,YAAY,UAAU,OAAO;AAC3B,UAAM,SAAS;AACf,eAAW,KAAK,IAAI,KAAK,IAAI,UAAU,CAAC,GAAG,CAAC;AAC5C,UAAM,MAAM,OAAO,aAAa;AAChC,UAAM,MAAM,OAAO,aAAa;AAChC,UAAM,WAAW,MAAM,OAAO,WAAW;AACzC,WAAO,YAAY,SAAS,OAAO,UAAU,cAAc,IAAI,KAAK;AACpE,WAAO,kBAAkB;AACzB,WAAO,oBAAoB;AAAA,EAC7B;AAAA,EACA,uBAAuB;AACrB,UAAM,SAAS;AACf,QAAI,CAAC,OAAO,OAAO,gBAAgB,CAAC,OAAO,GAAI;AAC/C,UAAM,MAAM,OAAO,GAAG,UAAU,MAAM,GAAG,EAAE,OAAO,eAAa;AAC7D,aAAO,UAAU,QAAQ,QAAQ,MAAM,KAAK,UAAU,QAAQ,OAAO,OAAO,sBAAsB,MAAM;AAAA,IAC1G,CAAC;AACD,WAAO,KAAK,qBAAqB,IAAI,KAAK,GAAG,CAAC;AAAA,EAChD;AAAA,EACA,gBAAgB,SAAS;AACvB,UAAM,SAAS;AACf,QAAI,OAAO,UAAW,QAAO;AAC7B,WAAO,QAAQ,UAAU,MAAM,GAAG,EAAE,OAAO,eAAa;AACtD,aAAO,UAAU,QAAQ,cAAc,MAAM,KAAK,UAAU,QAAQ,OAAO,OAAO,UAAU,MAAM;AAAA,IACpG,CAAC,EAAE,KAAK,GAAG;AAAA,EACb;AAAA,EACA,oBAAoB;AAClB,UAAM,SAAS;AACf,QAAI,CAAC,OAAO,OAAO,gBAAgB,CAAC,OAAO,GAAI;AAC/C,UAAM,UAAU,CAAC;AACjB,WAAO,OAAO,QAAQ,aAAW;AAC/B,YAAM,aAAa,OAAO,gBAAgB,OAAO;AACjD,cAAQ,KAAK;AAAA,QACX;AAAA,QACA;AAAA,MACF,CAAC;AACD,aAAO,KAAK,eAAe,SAAS,UAAU;AAAA,IAChD,CAAC;AACD,WAAO,KAAK,iBAAiB,OAAO;AAAA,EACtC;AAAA,EACA,qBAAqB,MAAM,OAAO;AAChC,QAAI,SAAS,QAAQ;AACnB,aAAO;AAAA,IACT;AACA,QAAI,UAAU,QAAQ;AACpB,cAAQ;AAAA,IACV;AACA,UAAM,SAAS;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,MAAM;AAAA,MACN;AAAA,IACF,IAAI;AACJ,QAAI,MAAM;AACV,QAAI,OAAO,OAAO,kBAAkB,SAAU,QAAO,OAAO;AAC5D,QAAI,OAAO,gBAAgB;AACzB,UAAI,YAAY,OAAO,WAAW,IAAI,KAAK,KAAK,OAAO,WAAW,EAAE,eAAe,IAAI;AACvF,UAAI;AACJ,eAAS,IAAI,cAAc,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACvD,YAAI,OAAO,CAAC,KAAK,CAAC,WAAW;AAC3B,uBAAa,KAAK,KAAK,OAAO,CAAC,EAAE,eAAe;AAChD,iBAAO;AACP,cAAI,YAAY,WAAY,aAAY;AAAA,QAC1C;AAAA,MACF;AACA,eAAS,IAAI,cAAc,GAAG,KAAK,GAAG,KAAK,GAAG;AAC5C,YAAI,OAAO,CAAC,KAAK,CAAC,WAAW;AAC3B,uBAAa,OAAO,CAAC,EAAE;AACvB,iBAAO;AACP,cAAI,YAAY,WAAY,aAAY;AAAA,QAC1C;AAAA,MACF;AAAA,IACF,OAAO;AAEL,UAAI,SAAS,WAAW;AACtB,iBAAS,IAAI,cAAc,GAAG,IAAI,OAAO,QAAQ,KAAK,GAAG;AACvD,gBAAM,cAAc,QAAQ,WAAW,CAAC,IAAI,gBAAgB,CAAC,IAAI,WAAW,WAAW,IAAI,aAAa,WAAW,CAAC,IAAI,WAAW,WAAW,IAAI;AAClJ,cAAI,aAAa;AACf,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF,OAAO;AAEL,iBAAS,IAAI,cAAc,GAAG,KAAK,GAAG,KAAK,GAAG;AAC5C,gBAAM,cAAc,WAAW,WAAW,IAAI,WAAW,CAAC,IAAI;AAC9D,cAAI,aAAa;AACf,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA,EACA,SAAS;AACP,UAAM,SAAS;AACf,QAAI,CAAC,UAAU,OAAO,UAAW;AACjC,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,IACF,IAAI;AAEJ,QAAI,OAAO,aAAa;AACtB,aAAO,cAAc;AAAA,IACvB;AACA,KAAC,GAAG,OAAO,GAAG,iBAAiB,kBAAkB,CAAC,EAAE,QAAQ,aAAW;AACrE,UAAI,QAAQ,UAAU;AACpB,6BAAqB,QAAQ,OAAO;AAAA,MACtC;AAAA,IACF,CAAC;AACD,WAAO,WAAW;AAClB,WAAO,aAAa;AACpB,WAAO,eAAe;AACtB,WAAO,oBAAoB;AAC3B,aAASY,gBAAe;AACtB,YAAM,iBAAiB,OAAO,eAAe,OAAO,YAAY,KAAK,OAAO;AAC5E,YAAM,eAAe,KAAK,IAAI,KAAK,IAAI,gBAAgB,OAAO,aAAa,CAAC,GAAG,OAAO,aAAa,CAAC;AACpG,aAAO,aAAa,YAAY;AAChC,aAAO,kBAAkB;AACzB,aAAO,oBAAoB;AAAA,IAC7B;AACA,QAAI;AACJ,QAAI,OAAO,YAAY,OAAO,SAAS,WAAW,CAAC,OAAO,SAAS;AACjE,MAAAA,cAAa;AACb,UAAI,OAAO,YAAY;AACrB,eAAO,iBAAiB;AAAA,MAC1B;AAAA,IACF,OAAO;AACL,WAAK,OAAO,kBAAkB,UAAU,OAAO,gBAAgB,MAAM,OAAO,SAAS,CAAC,OAAO,gBAAgB;AAC3G,cAAM,SAAS,OAAO,WAAW,OAAO,QAAQ,UAAU,OAAO,QAAQ,SAAS,OAAO;AACzF,qBAAa,OAAO,QAAQ,OAAO,SAAS,GAAG,GAAG,OAAO,IAAI;AAAA,MAC/D,OAAO;AACL,qBAAa,OAAO,QAAQ,OAAO,aAAa,GAAG,OAAO,IAAI;AAAA,MAChE;AACA,UAAI,CAAC,YAAY;AACf,QAAAA,cAAa;AAAA,MACf;AAAA,IACF;AACA,QAAI,OAAO,iBAAiB,aAAa,OAAO,UAAU;AACxD,aAAO,cAAc;AAAA,IACvB;AACA,WAAO,KAAK,QAAQ;AAAA,EACtB;AAAA,EACA,gBAAgB,cAAc,YAAY;AACxC,QAAI,eAAe,QAAQ;AACzB,mBAAa;AAAA,IACf;AACA,UAAM,SAAS;AACf,UAAM,mBAAmB,OAAO,OAAO;AACvC,QAAI,CAAC,cAAc;AAEjB,qBAAe,qBAAqB,eAAe,aAAa;AAAA,IAClE;AACA,QAAI,iBAAiB,oBAAoB,iBAAiB,gBAAgB,iBAAiB,YAAY;AACrG,aAAO;AAAA,IACT;AACA,WAAO,GAAG,UAAU,OAAO,GAAG,OAAO,OAAO,sBAAsB,GAAG,gBAAgB,EAAE;AACvF,WAAO,GAAG,UAAU,IAAI,GAAG,OAAO,OAAO,sBAAsB,GAAG,YAAY,EAAE;AAChF,WAAO,qBAAqB;AAC5B,WAAO,OAAO,YAAY;AAC1B,WAAO,OAAO,QAAQ,aAAW;AAC/B,UAAI,iBAAiB,YAAY;AAC/B,gBAAQ,MAAM,QAAQ;AAAA,MACxB,OAAO;AACL,gBAAQ,MAAM,SAAS;AAAA,MACzB;AAAA,IACF,CAAC;AACD,WAAO,KAAK,iBAAiB;AAC7B,QAAI,WAAY,QAAO,OAAO;AAC9B,WAAO;AAAA,EACT;AAAA,EACA,wBAAwB,WAAW;AACjC,UAAM,SAAS;AACf,QAAI,OAAO,OAAO,cAAc,SAAS,CAAC,OAAO,OAAO,cAAc,MAAO;AAC7E,WAAO,MAAM,cAAc;AAC3B,WAAO,eAAe,OAAO,OAAO,cAAc,gBAAgB,OAAO;AACzE,QAAI,OAAO,KAAK;AACd,aAAO,GAAG,UAAU,IAAI,GAAG,OAAO,OAAO,sBAAsB,KAAK;AACpE,aAAO,GAAG,MAAM;AAAA,IAClB,OAAO;AACL,aAAO,GAAG,UAAU,OAAO,GAAG,OAAO,OAAO,sBAAsB,KAAK;AACvE,aAAO,GAAG,MAAM;AAAA,IAClB;AACA,WAAO,OAAO;AAAA,EAChB;AAAA,EACA,MAAM,SAAS;AACb,UAAM,SAAS;AACf,QAAI,OAAO,QAAS,QAAO;AAG3B,QAAI,KAAK,WAAW,OAAO,OAAO;AAClC,QAAI,OAAO,OAAO,UAAU;AAC1B,WAAK,SAAS,cAAc,EAAE;AAAA,IAChC;AACA,QAAI,CAAC,IAAI;AACP,aAAO;AAAA,IACT;AACA,OAAG,SAAS;AACZ,QAAI,GAAG,cAAc,GAAG,WAAW,QAAQ,GAAG,WAAW,KAAK,aAAa,OAAO,OAAO,sBAAsB,YAAY,GAAG;AAC5H,aAAO,YAAY;AAAA,IACrB;AACA,UAAM,qBAAqB,MAAM;AAC/B,aAAO,KAAK,OAAO,OAAO,gBAAgB,IAAI,KAAK,EAAE,MAAM,GAAG,EAAE,KAAK,GAAG,CAAC;AAAA,IAC3E;AACA,UAAM,aAAa,MAAM;AACvB,UAAI,MAAM,GAAG,cAAc,GAAG,WAAW,eAAe;AACtD,cAAM,MAAM,GAAG,WAAW,cAAc,mBAAmB,CAAC;AAE5D,eAAO;AAAA,MACT;AACA,aAAO,gBAAgB,IAAI,mBAAmB,CAAC,EAAE,CAAC;AAAA,IACpD;AAEA,QAAI,YAAY,WAAW;AAC3B,QAAI,CAAC,aAAa,OAAO,OAAO,gBAAgB;AAC9C,kBAAY,cAAc,OAAO,OAAO,OAAO,YAAY;AAC3D,SAAG,OAAO,SAAS;AACnB,sBAAgB,IAAI,IAAI,OAAO,OAAO,UAAU,EAAE,EAAE,QAAQ,aAAW;AACrE,kBAAU,OAAO,OAAO;AAAA,MAC1B,CAAC;AAAA,IACH;AACA,WAAO,OAAO,QAAQ;AAAA,MACpB;AAAA,MACA;AAAA,MACA,UAAU,OAAO,aAAa,CAAC,GAAG,WAAW,KAAK,aAAa,GAAG,WAAW,OAAO;AAAA,MACpF,QAAQ,OAAO,YAAY,GAAG,WAAW,OAAO;AAAA,MAChD,SAAS;AAAA;AAAA,MAET,KAAK,GAAG,IAAI,YAAY,MAAM,SAAS,aAAa,IAAI,WAAW,MAAM;AAAA,MACzE,cAAc,OAAO,OAAO,cAAc,iBAAiB,GAAG,IAAI,YAAY,MAAM,SAAS,aAAa,IAAI,WAAW,MAAM;AAAA,MAC/H,UAAU,aAAa,WAAW,SAAS,MAAM;AAAA,IACnD,CAAC;AACD,WAAO;AAAA,EACT;AAAA,EACA,KAAK,IAAI;AACP,UAAM,SAAS;AACf,QAAI,OAAO,YAAa,QAAO;AAC/B,UAAM,UAAU,OAAO,MAAM,EAAE;AAC/B,QAAI,YAAY,MAAO,QAAO;AAC9B,WAAO,KAAK,YAAY;AAGxB,QAAI,OAAO,OAAO,aAAa;AAC7B,aAAO,cAAc;AAAA,IACvB;AAGA,WAAO,WAAW;AAGlB,WAAO,WAAW;AAGlB,WAAO,aAAa;AACpB,QAAI,OAAO,OAAO,eAAe;AAC/B,aAAO,cAAc;AAAA,IACvB;AAGA,QAAI,OAAO,OAAO,cAAc,OAAO,SAAS;AAC9C,aAAO,cAAc;AAAA,IACvB;AAGA,QAAI,OAAO,OAAO,QAAQ,OAAO,WAAW,OAAO,OAAO,QAAQ,SAAS;AACzE,aAAO,QAAQ,OAAO,OAAO,eAAe,OAAO,QAAQ,cAAc,GAAG,OAAO,OAAO,oBAAoB,OAAO,IAAI;AAAA,IAC3H,OAAO;AACL,aAAO,QAAQ,OAAO,OAAO,cAAc,GAAG,OAAO,OAAO,oBAAoB,OAAO,IAAI;AAAA,IAC7F;AAGA,QAAI,OAAO,OAAO,MAAM;AACtB,aAAO,WAAW,QAAW,IAAI;AAAA,IACnC;AAGA,WAAO,aAAa;AACpB,UAAM,eAAe,CAAC,GAAG,OAAO,GAAG,iBAAiB,kBAAkB,CAAC;AACvE,QAAI,OAAO,WAAW;AACpB,mBAAa,KAAK,GAAG,OAAO,OAAO,iBAAiB,kBAAkB,CAAC;AAAA,IACzE;AACA,iBAAa,QAAQ,aAAW;AAC9B,UAAI,QAAQ,UAAU;AACpB,6BAAqB,QAAQ,OAAO;AAAA,MACtC,OAAO;AACL,gBAAQ,iBAAiB,QAAQ,OAAK;AACpC,+BAAqB,QAAQ,EAAE,MAAM;AAAA,QACvC,CAAC;AAAA,MACH;AAAA,IACF,CAAC;AACD,YAAQ,MAAM;AAGd,WAAO,cAAc;AACrB,YAAQ,MAAM;AAGd,WAAO,KAAK,MAAM;AAClB,WAAO,KAAK,WAAW;AACvB,WAAO;AAAA,EACT;AAAA,EACA,QAAQ,gBAAgB,aAAa;AACnC,QAAI,mBAAmB,QAAQ;AAC7B,uBAAiB;AAAA,IACnB;AACA,QAAI,gBAAgB,QAAQ;AAC1B,oBAAc;AAAA,IAChB;AACA,UAAM,SAAS;AACf,UAAM;AAAA,MACJ;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAI,OAAO,OAAO,WAAW,eAAe,OAAO,WAAW;AAC5D,aAAO;AAAA,IACT;AACA,WAAO,KAAK,eAAe;AAG3B,WAAO,cAAc;AAGrB,WAAO,aAAa;AAGpB,QAAI,OAAO,MAAM;AACf,aAAO,YAAY;AAAA,IACrB;AAGA,QAAI,aAAa;AACf,aAAO,cAAc;AACrB,UAAI,MAAM,OAAO,OAAO,UAAU;AAChC,WAAG,gBAAgB,OAAO;AAAA,MAC5B;AACA,UAAI,WAAW;AACb,kBAAU,gBAAgB,OAAO;AAAA,MACnC;AACA,UAAI,UAAU,OAAO,QAAQ;AAC3B,eAAO,QAAQ,aAAW;AACxB,kBAAQ,UAAU,OAAO,OAAO,mBAAmB,OAAO,wBAAwB,OAAO,kBAAkB,OAAO,gBAAgB,OAAO,cAAc;AACvJ,kBAAQ,gBAAgB,OAAO;AAC/B,kBAAQ,gBAAgB,yBAAyB;AAAA,QACnD,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO,KAAK,SAAS;AAGrB,WAAO,KAAK,OAAO,eAAe,EAAE,QAAQ,eAAa;AACvD,aAAO,IAAI,SAAS;AAAA,IACtB,CAAC;AACD,QAAI,mBAAmB,OAAO;AAC5B,UAAI,OAAO,MAAM,OAAO,OAAO,OAAO,UAAU;AAC9C,eAAO,GAAG,SAAS;AAAA,MACrB;AACA,kBAAY,MAAM;AAAA,IACpB;AACA,WAAO,YAAY;AACnB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,eAAe,aAAa;AACjC,WAAO,kBAAkB,WAAW;AAAA,EACtC;AAAA,EACA,WAAW,mBAAmB;AAC5B,WAAO;AAAA,EACT;AAAA,EACA,WAAW,WAAW;AACpB,WAAO;AAAA,EACT;AAAA,EACA,OAAO,cAAc,KAAK;AACxB,QAAI,CAAC,QAAO,UAAU,YAAa,SAAO,UAAU,cAAc,CAAC;AACnE,UAAM,UAAU,QAAO,UAAU;AACjC,QAAI,OAAO,QAAQ,cAAc,QAAQ,QAAQ,GAAG,IAAI,GAAG;AACzD,cAAQ,KAAK,GAAG;AAAA,IAClB;AAAA,EACF;AAAA,EACA,OAAO,IAAI,QAAQ;AACjB,QAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,aAAO,QAAQ,OAAK,QAAO,cAAc,CAAC,CAAC;AAC3C,aAAO;AAAA,IACT;AACA,YAAO,cAAc,MAAM;AAC3B,WAAO;AAAA,EACT;AACF;AACA,OAAO,KAAK,UAAU,EAAE,QAAQ,oBAAkB;AAChD,SAAO,KAAK,WAAW,cAAc,CAAC,EAAE,QAAQ,iBAAe;AAC7D,WAAO,UAAU,WAAW,IAAI,WAAW,cAAc,EAAE,WAAW;AAAA,EACxE,CAAC;AACH,CAAC;AACD,OAAO,IAAI,CAAC,QAAQ,QAAQ,CAAC;", "names": ["window", "document", "support", "observerUpdate", "events", "slide", "translate", "realIndex", "minTranslate", "maxTranslate", "transitionEnd", "browser", "slideTo", "setTranslate", "i", "increment", "breakpoints"]}