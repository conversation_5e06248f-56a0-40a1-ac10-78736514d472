
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Arial', sans-serif;
}


body {
  line-height: 1.6;
  color: #333;
}

/* Hero Section */
.hero {
  position: relative;
  display: flex;
  align-items: center;
  height: 80vh;
  background: linear-gradient(to right, #fff, #f8f8f8);
  overflow: hidden;
}

.hero-overlay {
  position: absolute;
  width: 100%;
  height: 100%;
  background: rgba(255,255,255,0.6);
}

.hero-content {
  display: flex;
  align-items: center;
  justify-content: space-around;
  width: 100%;
  padding: 0 10%;
  z-index: 2;
}

.hero-content .text {
  max-width: 500px;
}

.hero-content h1 {
  font-size: 3rem;
  font-weight: bold;
  margin-bottom: 15px;
}

.hero-content h1 span {
  color: orange;
}

.hero-content p {
  font-size: 1.1rem;
  line-height: 1.5;
}

.hero-content p:hover::after{
  content: '';
  position: absolute;
  width: 500px;
  height: 2px;
  background: orange;
  bottom:210px;
  left: 230px;
  animation: slideIn 1s ease-in-out;

}
@keyframes slideIn {
  0% { width: 0; }
  100% { width: 500px; }
}

.video-container video {
  width: 400px;
  border-radius: 15px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
}
@media(min-width: 1340px){
.video-container :hover{
  position: relative;
  transform: scale(3);
  left: -250px;
}
}
/* Mission Section */
.mission {
  padding: 60px 20px;
  background: white;
  text-align: center;
}

.mission h2 {
  color: orange;
  font-size: 2rem;
  margin-bottom: 20px;
}

.mission p {
  max-width: 600px;
  margin: auto;
  font-size: 1.1rem;
  color: #555;
}

/* Team Section */
.team {
  padding: 60px 20px;
  background: #fafafa;
  text-align: center;
}

.team h2 {
  font-size: 2rem;
  color: orange;
  margin-bottom: 30px;
}

.team-members {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.member {
  background: white;
  padding: 20px;
  border-radius: 15px;
  box-shadow: 0 8px 25px rgba(0,0,0,0.1);
  width: 250px;
}

.member img {
  width: 100%;
  border-radius: 15px;
  margin-bottom: 15px;
}

.member h3 {
  font-size: 1.2rem;
  margin-bottom: 5px;
}

.member p {
  color: gray;
  font-size: 0.9rem;
}

/* Responsive */
@media(max-width: 768px) {
  .hero-content {
    flex-direction: column;
    text-align: center;
  }
  .video-container video {
    width: 90%;
  }
}
.navbar {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}


.logo a{
  color: #f97316;
   text-decoration: none;
  font-weight: bold;
  font-size: 30px;
}

.spacer {
  flex: 1 1 auto;
}

.nav-links {
  display: flex;
  gap: 25px;
}

.nav-link {
  color: white;
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  font-weight: 400px;
  position: relative;
  transition: color 0.3s ease;
  text-shadow: 2px 2px 4px rgba(250, 173, 18, 0.3);
}

.nav-link:hover {
  color: #f97316;
}

.nav-link:hover::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #f97316;
  bottom: -4px;
  left: 0;

}

.nav-link.active {
  color: #f97316;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #f97316;
  bottom: -4px;
  left: 0;
}

.login-btn {
  background: transparent;
  color: white;
  font-family: 'Lato', sans-serif;
  font-weight: bold;
  font-size: 14px;
  padding: 6px 12px;
  transition: color 0.3s ease;
}

.login-btn:hover {
  color: #f97316;
}
