:host{
    display: block;
    background: linear-gradient(20deg, #080808, #4d4847);
    height: 100vh;
}
.navbar {
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.7), transparent);
  position: fixed;
  top: 0;
  width: 100%;
  z-index: 1000;
  padding: 8px 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}


.logo a{
  color: #f97316;
   text-decoration: none;
  font-weight: bold;
  font-size: 30px;
}

.spacer {
  flex: 1 1 auto;
}

.nav-links {
  display: flex;
  gap: 12px;
}

.nav-link {
  color: white;
  font-family: 'Lato', sans-serif;
  font-size: 16px;
  font-weight: 400px;
  position: relative;
  transition: color 0.4s ease;
  text-shadow: 2px 2px 4px rgba(250, 173, 18, 0.3);
}

.nav-link:hover {
  color: #f97316;
}

.nav-link:hover::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #f97316;
  bottom: -4px;
  left: 0;
  animation: slideIn 0.4s ease-in-out;
}

.nav-link.active {
  color: #f97316;
}

.nav-link.active::after {
  content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #f97316;
  bottom: -4px;
  left: 0;
}

.login-btn {
  background: transparent;
  color: white;
  font-family: 'Lato', sans-serif;
  font-weight: bold;
  font-size: 14px;
  padding: 6px 12px;
  transition: color 0.3s ease;
  text-decoration: none;
}

.login-btn:hover {
  color: #f97316;
}
.login-btn:hover::after{
    content: '';
  position: absolute;
  width: 100%;
  height: 2px;
  background: #f97316;
  bottom: -4px;
  left: 0;
  animation: slideIn 0.4s ease-in-out;
}
.contactmain {
  display: block;
  text-align: center;
  padding: 50px 100px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #030303;
  color: white;
  font-size: 20px;
  border: 2px solid #f97316;
  overflow: hidden;
}
.contactmain h1{
  font-family: 'cursive';
  font-size: 50px;
  font-weight: bold;
  font-style: italic;
  text-shadow: 4px 2px 4px rgba(233, 233, 237, 0.3);
   padding-bottom: 10px;
}

.contactmain h1:hover{
  text-shadow: 4px 2px 4px rgba(233, 233, 237, 0.3);
  color: #f97316;
  text-decoration: underline;
  animation: slideIn 0.4s ease-in-out;
 
}
.contactmain p{
  font-size: 20px;
  font-family: 'Lato', sans-serif;
  font-weight: 400;
  line-height: 1.5;
  margin-bottom: 30px;
  color: #f97316; 
  text-shadow: 2px 2px 4px rgba(250, 173, 18, 0.3);
}
.contactmain .contactform{
  display: block;
  text-align: center;
  padding:10px 20px;
  border: 2px solid #f97316;
  border-radius: 10px;
  margin: 0 auto;
  width: 50%;
  color: #030303;

}