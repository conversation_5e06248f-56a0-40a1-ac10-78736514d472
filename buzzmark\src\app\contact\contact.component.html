<mat-toolbar class="navbar">
  <span class="logo" style="font-family: cursive;"><a href="/buzzmark/src/app/home/<USER>">Buzzmark</a></span>
  <span class="spacer"></span>
  <div class="nav-links">
    <a mat-button [routerLink]="['/']" class="nav-link" >Home</a>
    <a mat-button [routerLink]="['/about']" class="nav-link active" >About</a>
    <a mat-button href="#contact" class="nav-link" >Contact</a>
  </div>
  <span class="spacer"></span>
  <a mat-button [routerLink]="['/login']" class="login-btn">Login</a>
</mat-toolbar>
<div class="contactmain">
    <h1>Contact Us</h1>
    <p> 
        We'd love to hear from you! Whether you have a question, a suggestion, or just want to say hello, we're here to help.
    </p>
<div class="contactform">
  <form (ngSubmit)="sendEmail()" #contactForm="ngForm">
    <!-- Username -->
    <input 
      type="text" 
      placeholder="Name" 
      [(ngModel)]="username" 
      name="username" 
      required
      style="width: 100%; height: 40px; border-radius: 10px; border: 2px solid #f97316; margin-bottom: 10px; padding: 10px;"
    >

    <!-- Email -->
    <input 
      type="email" 
      placeholder="Email" 
      [(ngModel)]="email" 
      name="email" 
      required
      style="width: 100%; height: 40px; border-radius: 10px; border: 2px solid #f97316; margin-bottom: 10px; padding: 10px;"
    >

    <!-- Message -->
    <textarea 
      placeholder="Message" 
      [(ngModel)]="message" 
      name="message" 
      required
      style="width: 100%; height: 100px; border-radius: 10px; border: 2px solid #f97316; margin-bottom: 10px; padding: 10px;"
    ></textarea>

    <!-- Send Button -->
    <button 
      type="submit" 
      style="width: 100%; height: 40px; border-radius: 10px; border: 2px solid #f97316; margin-bottom: 10px; padding: 10px; color: white; background-color: #f97316;"
    >
      Send
    </button>
  </form>
</div>
