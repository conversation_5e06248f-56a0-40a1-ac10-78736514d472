import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import{ MatToolbarModule } from '@angular/material/toolbar';
import{RouterLink} from '@angular/router';
import { HttpClient } from '@angular/common/http';
import { FormsModule } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import Swal from 'sweetalert2';
import { error } from 'console';
@Component({
  selector: 'app-contact',
  imports: [FormsModule,CommonModule,MatToolbarModule,RouterLink,],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.css'
})
export class ContactComponent {
      username: string = '';
      email: string = '';
      message: string = '';
     constructor(
       private http: HttpClient,
      
       private router: Router,
       private route: ActivatedRoute
     ) {
       this.route.queryParams.subscribe(params => {
         this.username = params['email'] || '';
         this.email = params['email'] || '';
          this.message = params['message'] || '';
        });
     }
  sendEmail() {
    const data = {
    username: this.username,
    email: this.email,
    message: this.message
  };

    this.http.post('http://localhost:8000/api/contact',data).subscribe({  
      next: (response: any) => {
       
      Swal.fire({
        title: 'Message sent',
        text: 'Your message has been sent successfully.',
        icon: 'success',
        timer: 3000
      }).then(() => {
        this.router.navigate(['/home']);
      });
      },
      error: (error: any) => {
        console.error('Error sending message:', error);
        Swal.fire({
          title: 'Error',
          text: 'Failed to send message.',
          icon: 'error',
          timer: 3000
        });
      }
    });
}
}
