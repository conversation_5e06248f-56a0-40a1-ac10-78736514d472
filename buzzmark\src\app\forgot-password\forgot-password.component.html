<div class="logo" style="position: absolute; top: 20px; left: 20px;">
  <h1 style="color: #f97316; font-weight: 600; text-align: center;font-family: cursive;font-size: 25px;">
    <a href="/buzzmark/src/app/home/<USER>">Buzzmark</a>
  </h1>
</div>

<!-- Étape 1 : Email -->
<div *ngIf="step === 1">
  <div class="forgot-container">
    <div class="forgot">
      <h2 class="form-title">Forgot Password</h2>
      <p>Enter your email address to reset your password</p>
      <input type="email" placeholder="Email" [(ngModel)]="email" required>
      <button (click)="sendCode()">Send Code</button>
    </div>
  </div>
  <div class="forgot2"></div>
</div>

<!-- Étape 2 : Code -->
<div *ngIf="step === 2">
  <div class="forgot-container">
    <div class="forgot">
      <h2 class="form-title">Verify Code</h2>
      <p>Enter the code sent to your email</p>
      <input type="text" placeholder="Code" [(ngModel)]="code" required>
      <button (click)="verifyCode()">Verify Code</button>
    </div>
  </div>
  <div class="forgot2"></div>
</div>

<!-- Étape 3 : Nouveau mot de passe -->
<div *ngIf="step === 3">
  <div class="forgot-container">
    <div class="forgot">
      <h2 class="form-title">New Password</h2>
      <p>Enter your new password</p>
      <input type="password" placeholder="New Password" [(ngModel)]="newPassword" required>
      <button (click)="resetPassword()">Reset Password</button>
    </div>
  </div>
  <div class="forgot2"></div>
</div>
