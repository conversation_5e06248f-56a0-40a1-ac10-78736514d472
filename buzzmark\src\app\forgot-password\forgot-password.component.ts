import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { HttpClient } from '@angular/common/http';
import { ActivatedRoute, Router } from '@angular/router';
@Component({
  selector: 'app-forgot-password',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './forgot-password.component.html',
  styleUrls: ['./forgot-password.component.css']
})
export class ForgotPasswordComponent {
  step = 1;
  email = '';
  code = '';
  newPassword = '';
constructor(private http: HttpClient, private route: ActivatedRoute, private router: Router) {}

  ngOnInit() {
    this.email = this.route.snapshot.queryParams['email'] || '';
  }
  sendCode() {
    if (!this.email) return;
    console.log('Code sent to:', this.email);
    this.step = 2;
    this.http.post('http://localhost:8000/api/send-code', { email: this.email }).subscribe({
      next: (response: any) => {
        console.log('Code sent to:', this.email);
        this.step = 2;
      },
      error: (error) => {
        console.error('Error sending code:', error);
      }
    });
  }

  verifyCode() {
    if (!this.code) return;
    console.log('Code verified:', this.code);
    this.step = 3;
    this.http.post('http://localhost:8000/api/verify-code', { email: this.email, code: this.code }).subscribe({
      next: (response: any) => {
        this.step = 3;
      },
      error: (error) => {
        console.error('Error verifying code:', error);
      }
    });
  }

  resetPassword() {
  if (!this.email || !this.code || !this.newPassword) {
    alert('Please fill in all fields');
    return;
  }

  const payload = {
    email: this.email,
    code: this.code,
    password: this.newPassword,
    password_confirmation: this.newPassword
  };

  this.http.post('http://localhost:8000/api/reset-password', payload).subscribe({
    next: (response: any) => {
      console.log('Password reset:', response);
      alert('Password reset successfully!');
      // maintenant on peut reset
      this.step = 1;
      this.email = '';
      this.code = '';
      this.newPassword = '';
    },
    error: (error) => {
      console.error('Error resetting password:', error);
      alert(error?.error?.message || 'Reset failed');
    }
  });
}
}
