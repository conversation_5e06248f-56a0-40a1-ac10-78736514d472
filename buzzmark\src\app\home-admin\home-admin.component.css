/* Variables globales */
:root {
  --primary-color: #4361ee;
  --primary-gradient: linear-gradient(135deg, #4361ee, #3a56d4);
  --secondary-color: #4cc9f0;
  --secondary-gradient: linear-gradient(135deg, #4cc9f0, #3db8df);
  --dark-color: #333;
  --light-color: #f4f4f4;
  --danger-color: #f72585;
  --danger-gradient: linear-gradient(135deg, #f72585, #e01f71);
  --success-color: #4cc9f0;
  --success-gradient: linear-gradient(135deg, #4cc9f0, #3db8df);
  --warning-color: #f8961e;
  --warning-gradient: linear-gradient(135deg, #f8961e, #e88613);
  --sidebar-width: 280px;
  --header-height: 70px;
  --card-shadow: 0 10px 20px rgba(0, 0, 0, 0.08);
  --hover-shadow: 0 15px 30px rgba(0, 0, 0, 0.12);
  --transition-speed: 0.3s;
}

/* Styles pour le conteneur principal */
.container {
  @apply flex min-h-screen relative;
}

/* Sidebar */
.sidebar {
  @apply w-[var(--sidebar-width)] bg-white dark:bg-[#1e1e1e] shadow-[5px_0_25px_rgba(0,0,0,0.05)] fixed h-full z-[100] transition-all duration-[var(--transition-speed)] rounded-r-[20px] overflow-hidden;
}

.sidebar__logo {
  @apply flex items-center justify-center p-5 border-b border-[rgba(0,0,0,0.05)];
}

/* Main Content */
.main-content {
  @apply flex-1 ml-[var(--sidebar-width)] p-8 transition-all duration-[var(--transition-speed)];
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Header */
.nav__header {
  @apply flex justify-between items-center mb-10 pb-5 border-b border-[rgba(0,0,0,0.05)] relative;
}

.nav__header__left {
  @apply flex items-center;
}

.nav__header__left i {
  @apply text-2xl mr-2 text-primary dark:text-primaryDark transition-all duration-[var(--transition-speed)];
}

.nav__header::after {
  @apply content-[''] absolute bottom-[-1px] left-0 w-20 h-0.5 bg-primary rounded-[10px];
}

.nav__header__right {
  @apply flex items-center;
}

.admin-info {
  @apply mr-5 text-sm text-dark dark:text-[#e0e0e0] bg-[rgba(67,97,238,0.1)] py-2 px-4 rounded-[20px] font-medium transition-all duration-[var(--transition-speed)] hover:bg-[rgba(67,97,238,0.15)] hover:-translate-y-0.5;
}

.dark-mode-toggle {
  @apply border-none cursor-pointer text-white w-10 h-10 rounded-full flex items-center justify-center transition-all duration-[var(--transition-speed)] shadow-[0_5px_15px_rgba(67,97,238,0.3)] hover:-translate-y-1 hover:rotate-12 hover:shadow-[0_8px_20px_rgba(67,97,238,0.4)];
}

.dark-mode-toggle i {
  @apply text-xl transition-all duration-[var(--transition-speed)];
}

/* Dark Mode */
:host-context(body.dark-mode) {
  @apply bg-[#121212] text-light;
}

:host-context(body.dark-mode) .sidebar {
  @apply bg-[#1e1e1e] shadow-[5px_0_25px_rgba(0,0,0,0.2)];
}

:host-context(body.dark-mode) .sidebar__logo {
  @apply border-b-[rgba(255,255,255,0.05)];
}

:host-context(body.dark-mode) .sidebar__nav a {
  @apply text-[#e0e0e0];
}

:host-context(body.dark-mode) .sidebar__nav a:hover,
:host-context(body.dark-mode) .sidebar__nav a.active {
  @apply bg-[rgba(255,255,255,0.05)];
}

:host-context(body.dark-mode) .nav__header h1 {
  @apply text-light;
}

:host-context(body.dark-mode) .admin-info {
  @apply text-[#e0e0e0] bg-[rgba(255,255,255,0.05)];
}

:host-context(body.dark-mode) .dark-mode-toggle {
  @apply bg-danger shadow-[0_5px_15px_rgba(247,37,133,0.3)] hover:shadow-[0_8px_20px_rgba(247,37,133,0.4)];
}

/* Styles pour les sous-pages */
.section__container {
  @apply mb-10;
}

.table-container {
  @apply bg-white dark:bg-[#1e1e1e] rounded-[15px] p-8 shadow-[var(--card-shadow)] transition-all duration-[var(--transition-speed)] hover:shadow-[var(--hover-shadow)];
}

.table-header {
  @apply flex justify-between items-center mb-6;
}

.section__header {
  @apply relative text-2xl font-semibold text-dark dark:text-light pb-3;
}

.section__header::after {
  @apply content-[''] absolute bottom-0 left-0 w-1/2 h-0.5 bg-primary rounded-[10px];
}

.btn-add {
  @apply flex items-center bg-primary text-white border-none py-2 px-5 rounded-[30px] text-sm font-medium transition-all duration-[var(--transition-speed)] shadow-[0_5px_15px_rgba(67,97,238,0.3)] hover:-translate-y-1 hover:shadow-[0_8px_20px_rgba(67,97,238,0.4)] relative overflow-hidden;
}

.btn-add::before {
  @apply content-[''] absolute top-0 left-[-100%] w-full h-full bg-primary;
  transition: all 600ms ease-in-out;
}

.btn-add:hover::before {
  @apply left-[100%];
}

.filter-input {
  @apply w-full p-3 border border-[rgba(0,0,0,0.1)] rounded-[30px] mb-6 text-sm transition-all duration-[var(--transition-speed)] shadow-[0_2px_10px_rgba(0,0,0,0.02)] focus:outline-none focus:border-primary focus:shadow-[0_5px_15px_rgba(67,97,238,0.1)] dark:bg-[#2a2a2a] dark:border-[rgba(255,255,255,0.1)] dark:text-light;
}

.table-responsive {
  @apply overflow-x-auto rounded-[10px];
}

table {
  @apply w-full border-separate border-spacing-0;
}

th {
  @apply p-4 text-left bg-[rgba(67,97,238,0.05)] font-semibold text-dark dark:bg-[rgba(255,255,255,0.05)] dark:text-[#e0e0e0] border-b-2 border-[rgba(67,97,238,0.1)] dark:border-[rgba(255,255,255,0.1)];
}

th:first-child {
  @apply rounded-tl-[10px];
}

th:last-child {
  @apply rounded-tr-[10px];
}

tbody tr {
  @apply transition-all duration-[var(--transition-speed)] hover:bg-[rgba(67,97,238,0.03)] hover:-translate-y-0.5 hover:shadow-[0_5px_15px_rgba(0,0,0,0.05)];
}

td {
  @apply p-4 border-b border-[rgba(0,0,0,0.05)] dark:border-[rgba(255,255,255,0.05)];
}

.card {
  @apply bg-white dark:bg-[#1e1e1e] rounded-[15px] p-6 shadow-[var(--card-shadow)] text-center transition-all duration-[var(--transition-speed)] relative overflow-hidden hover:-translate-y-2 hover:shadow-[var(--hover-shadow)];
}

.card::before {
  @apply content-[''] absolute top-0 left-0 w-full h-0 bg-[rgba(67,97,238,0.05)] transition-all duration-500 z-[-1];
}

.card:hover::before {
  @apply h-full;
}

.card__header {
  @apply mb-5;
}

.card__icon {
  @apply w-[70px] h-[70px] rounded-full text-white text-2xl flex items-center justify-center shadow-[0_10px_20px_rgba(0,0,0,0.1)] transition-all duration-[var(--transition-speed)] hover:scale-110 relative;
}

.card__icon::after {
  @apply content-[''] absolute top-[-5px] left-[-5px] right-[-5px] bottom-[-5px] rounded-full border-2 border-transparent opacity-0 transition-all duration-[var(--transition-speed)];
}

.card:hover .card__icon::after {
  @apply border-[rgba(255,255,255,0.2)] opacity-100;
}

.card__icon--clients {
  @apply bg-primary;
}

.card__icon--coaches {
  @apply bg-warning;
}

.card__icon--subscriptions {
  @apply bg-success;
}

.card h3 {
  @apply text-4xl font-bold text-primary;
}

.card p {
  @apply text-gray-500 dark:text-gray-400 font-medium text-lg relative;
}

.card p::after {
  @apply content-[''] absolute bottom-[-5px] left-1/2 -translate-x-1/2 w-0 h-0.5 bg-primary transition-all duration-[var(--transition-speed)];
}

.card:hover p::after {
  @apply w-1/2;
}

.modal-form {
  @apply space-y-6;
}

.form-group {
  @apply mb-6;
}

.form-group label {
  @apply block mb-2 font-semibold text-dark dark:text-[#e0e0e0] text-sm;
}

.form-group input,
.form-group textarea,
.form-group select {
  @apply w-full p-3 border border-[rgba(0,0,0,0.1)] rounded-[10px] text-base transition-all duration-[var(--transition-speed)] shadow-[0_2px_10px_rgba(0,0,0,0.02)] focus:outline-none focus:border-primary focus:shadow-[0_5px_15px_rgba(67,97,238,0.1)] dark:bg-[#2a2a2a] dark:border-[rgba(255,255,255,0.1)] dark:text-light;
}

.form-actions {
  @apply flex justify-end;
}

.btn-primary {
  @apply bg-primary text-white border-none py-3 px-6 rounded-[30px] text-base font-medium transition-all duration-[var(--transition-speed)] shadow-[0_5px_15px_rgba(67,97,238,0.3)] hover:-translate-y-1 hover:shadow-[0_8px_20px_rgba(67,97,238,0.4)] relative overflow-hidden;
}

.btn-primary::before {
  @apply content-[''] absolute top-0 left-[-100%] w-full h-full bg-primary;
  transition: all 600ms ease-in-out;
}

.btn-primary:hover::before {
  @apply left-[100%];
}

/* Responsive Styles */
@media (max-width: 992px) {
  .sidebar {
    @apply w-[260px];
  }
  .main-content {
    @apply ml-0 p-5;
  }
  .dashboard__cards {
    @apply grid-cols-1 md:grid-cols-2;
  }
  .card {
    @apply p-5;
  }
  .card__icon {
    @apply w-14 h-14 text-xl;
  }
  .card h3 {
    @apply text-3xl;
  }
}

@media (max-width: 768px) {
  .sidebar {
    @apply -translate-x-full z-[1001];
  }
  .sidebar.active {
    @apply translate-x-0;
  }
  .main-content.sidebar-active {
    @apply ml-[var(--sidebar-width)];
  }
  .nav__header {
    @apply mb-8;
  }
  .nav__header h1 {
    @apply text-2xl;
  }
}

@media (max-width: 576px) {
  .nav__header {
    @apply flex-col items-start gap-4;
  }
  .nav__header__right {
    @apply w-full justify-between;
  }
  .admin-info {
    @apply mr-0;
  }
  .nav__header__left i {
    @apply text-xl;
  }
}