<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css" />
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.1.3/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css">

<mat-toolbar class="navbar">
  <span class="logo" [routerLink]="['/']">Buzzmark</span>
  <span class="spacer"></span>
  <div class="nav-links">
    <a mat-button [routerLink]="['/']" class="nav-link active">Home</a>
    <a mat-button [routerLink]="['/about']" class="nav-link">About</a>
    <a mat-button href="#contact" class="nav-link">Contact</a>
  </div>
   
 
</mat-toolbar>
<div class="title">
    Buzzmark
   </div>

<div class="login-container">
  <mat-card class="login-card">
    <mat-card-header>
      <mat-card-title style="color:rgb(9, 9, 9)">Login <span style="color: #f97316;font-weight: bold;font-size:30px;">Buzzmark</span></mat-card-title>
      <mat-card-subtitle>Enter your email address and password to log in </mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <form class="login-form" (ngSubmit)="onSubmit()">
        <!-- Champ Email ou Nom d'utilisateur -->
        <mat-form-field appearance="fill" class="full-width">
          <mat-label>Email or UserName</mat-label>
          <input matInput [(ngModel)]="usernameOrEmail" name="usernameOrEmail" required>
          <mat-error *ngIf="usernameOrEmailError">this field is required</mat-error>
        </mat-form-field>

        <!-- Champ Mot de passe -->
        <mat-form-field appearance="fill" class="full-width">
  <mat-label>Password</mat-label>
  <input
    matInput
    [type]="hidePassword ? 'password' : 'text'"
    [(ngModel)]="password"
    name="password"
    required
  >
  <button
    mat-icon-button
    matSuffix
    (click)="hidePassword = !hidePassword"
    [attr.aria-label]="'Toggle password visibility'"
    type="button"
  >
  <mat-icon class="visibility-toggle">{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
  </button>
  <mat-error *ngIf="passwordError">this field is required</mat-error>
</mat-form-field>


        <!-- Type de client -->
        <mat-form-field appearance="fill" class="full-width">
          <mat-label>Client Type</mat-label>
          <mat-select [(ngModel)]="clientType" name="clientType" required>
            <mat-option value="entreprise">Company</mat-option>
            <mat-option value="influenceur">Influencer</mat-option>
          </mat-select>
          <mat-error *ngIf="clientTypeError">You must select a client type</mat-error>
        </mat-form-field>

        <!-- Bouton Connexion -->
        <button mat-raised-button color="primary" type="submit" class="full-width login-btn" (click)="onSubmit()">Log in</button>
      </form>

      <!-- Connexion Google (Placeholder) -->
      <div class="google-login" *ngIf="clientType === 'entreprise'">
        <p>Or Connect With Google</p>
        <button mat-raised-button color="primary" (click)="loginWithGoogle()">LogIn with Google</button>
      </div>
      <div class="ForgotPass">
        <a [routerLink]="['/forgot-password']">Forgot Password ?</a>

      </div>

      <!-- Lien vers inscription -->
      <p class="register-link">
        Don't have an account ? <a [routerLink]="['/register']">Sign up</a>
      </p>
    </mat-card-content>
  </mat-card>
</div>
<!-- Footer -->
<div class="footer-dark">
        <footer>
            <div class="container">
                <div class="row">
                    <div class="col-sm-6 col-md-3 item">
                        <h3>Services</h3>
                        <ul>
                            <li><a href="#">Marketing</a></li>
                            <li><a href="#">Market analytics</a></li>
                            <li><a href="#">AI services</a></li>
                        </ul>
                    </div>
                    <div class="col-sm-6 col-md-3 item">
                        <h3>About</h3>
                        <ul>
                            <li><a href="#">Company</a></li>
                            <li><a href="#">Team</a></li>
                            <li><a href="#">Collaborations</a></li>
                        </ul>
                    </div>
                    <div class="col-md-6 item text">
                        <h3>Buzzmark</h3>
                        <p>Web application that connects brands and influencers to create authentic and effective marketing campaigns.</p>
                    </div>
                    <div class="col item social"><a href="https://www.facebook.com/"><i class="icon ion-social-facebook"></i></a><a href="https://x.com/"><i class="icon ion-social-twitter"></i></a><a href="https://www.snapchat.com/web?ref=web_nav_chat_icon"><i class="icon ion-social-snapchat"></i></a><a href="https://www.instagram.com/"><i class="icon ion-social-instagram"></i></a></div>
                </div>
                <p class="copyright">Buzzmark © 2025</p>
            </div>
        </footer>
    </div>