.plans-container {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 30px;
  padding-top: 50px;
}

.plan-card {
  width: 250px;
  perspective: 1000px;
  border: none;
  cursor: pointer;
  transition: transform 0.4s ease;
}

.plan-inner {
  background: rgb(255, 255, 255);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 12px 20px rgba(0, 0, 0, 0.15);
  transform-style: preserve-3d;
  transition: transform 0.3s, box-shadow 0.3s;
}

.plan-card:hover .plan-inner {
  transform: rotateY(5deg) scale(1.03);
  box-shadow: 0 15px 25px rgba(0, 0, 0, 0.3);
}

.plan-card.selected .plan-inner {
  border: 2px solid #f4801a;
  background-color: #fff7ec;
}

.plan-inner h3 {
  margin-bottom: 5px;
  font-size: 20px;
  color: #333;
}

.plan-inner p {
  font-weight: bold;
  color: #f4801a;
  margin: 10px 0;
}

.plan-inner ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.plan-inner ul li {
  margin-bottom: 5px;
  font-size: 14px;
  color: #555;
}

.card-details {
  display: flex;
  gap: 20px;
  margin-top: 15px;
}

.full-width {
  width: 50%;
}
form button{
  color: azure;
  background-color: #1e3a8a;
  border-radius: 25px;
  padding: 10px;
  transition: transform 0.3s ease, background-color 0.3s ease;
}
form button:hover {
  transform: scale(1.05);
  background-color: #f97316;
}
 