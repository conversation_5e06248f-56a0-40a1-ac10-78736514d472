.profile-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #1e3a8a, #f97316);
  padding: 20px;
}

.profile-card {
  max-width: 400px;
  width: 100%;
  background: white;
  border-radius: 10px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  padding: 20px;
  animation: fadeIn 1s ease-in-out;
}

.profile-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.full-width {
  width: 100%;
}

.profile-btn {
  padding: 10px;
  font-size: 16px;
  transition: transform 0.3s ease;
}

.profile-btn:hover {
  transform: scale(1.05);
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}