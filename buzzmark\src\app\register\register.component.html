<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/twitter-bootstrap/4.1.3/css/bootstrap.min.css">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/ionicons/2.0.1/css/ionicons.min.css">
<link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
<mat-toolbar class="navbar">
  <span class="logo">Buzzmark</span>
  <span class="spacer"></span>
  <div class="nav-links">
    <a mat-button [routerLink]="['/']" class="nav-link active">Home</a>
    <a mat-button [routerLink]="['/about']" class="nav-link">About</a>
    <a mat-button href="#contact" class="nav-link">Contact</a>
  </div>
</mat-toolbar>
<div class="title">
  Buzzmark
</div>
<div class="register-container">
  <mat-card class="register-card">
    <mat-card-header>
      <mat-card-title>Sign up <span style="color: #f97316;font-size: 30px;font-weight: bold;">Buzzmark</span></mat-card-title>
      <mat-card-subtitle>Create your account to be part of the Buzzmark community</mat-card-subtitle>
    </mat-card-header>
    <mat-card-content>
      <form class="register-form" (ngSubmit)="onSubmit()">
        <!-- Type de client -->
        <mat-form-field appearance="fill" class="full-width">
          <mat-label>Client Type</mat-label>
          <mat-select [(ngModel)]="clientType" name="clientType" required (ngModelChange)="onClientTypeChange()">
            <mat-option value="entreprise">Company</mat-option>
            <mat-option value="influenceur">Influencer</mat-option>
          </mat-select>
          <mat-error *ngIf="clientTypeError">You must select a client type</mat-error>
        </mat-form-field>

        <!-- Champs pour Entreprise -->
        <div *ngIf="clientType === 'entreprise'">
          <mat-form-field appearance="fill" class="full-width">
            <mat-label>Company Name</mat-label>
            <input matInput [(ngModel)]="companyName" name="companyName" required>
            <mat-error *ngIf="companyNameError">This field is required</mat-error>
          </mat-form-field>
        </div>

        <!-- Champs pour Influenceur -->
        <div *ngIf="clientType === 'influenceur'">
          <mat-form-field appearance="fill" class="full-width">
            <mat-label>Full Name</mat-label>
            <input matInput [(ngModel)]="fullName" name="fullName" required>
            <mat-error *ngIf="fullNameError">This field is required</mat-error>
          </mat-form-field>
        </div>

        <!-- Email -->
        <mat-form-field appearance="fill" class="full-width">
          <mat-label>Email</mat-label>
          <input matInput [(ngModel)]="email" name="email" type="email" required>
          <mat-error *ngIf="emailError">You must enter a valid email</mat-error>
        </mat-form-field>

        <!-- Mot de passe -->
        <mat-form-field appearance="fill" class="full-width">
          <mat-label>Password</mat-label>
          <input matInput [(ngModel)]="password" name="password" [type]="passwordType" (input)="checkPasswordStrength()" required>
          <mat-icon matSuffix class="visibility-toggle" (click)="togglePasswordVisibility()" [attr.aria-label]="passwordType === 'password' ? 'Show password' : 'Hide password'">
            {{ passwordType === 'password' ? 'visibility_off' : 'visibility' }}
          </mat-icon>
          <mat-error *ngIf="passwordError">Password must be at least 8 characters, match the confirmation, and be at least Weak strength</mat-error>
        </mat-form-field>

        <!-- Indicateur de force du mot de passe avec emojis -->
        <div class="password-strength" [ngClass]="{
          'very-short': passwordStrength === 'Very Short',
          'very-weak': passwordStrength === 'Very Weak',
          'weak': passwordStrength === 'Weak',
          'good': passwordStrength === 'Good',
          'perfect': passwordStrength === 'Perfect'
        }" *ngIf="passwordStrength">
          Password Strength: 
          <span [ngSwitch]="passwordStrength">
            <span *ngSwitchCase="'Very Short'">😞 Very Short (8 characters minimum)</span>
            <span *ngSwitchCase="'Very Weak'">🚫 Very Weak</span>
            <span *ngSwitchCase="'Weak'">😐 Weak</span>
            <span *ngSwitchCase="'Good'">😊 Good</span>
            <span *ngSwitchCase="'Perfect'">🌟 Perfect</span>
          </span>
        </div>

        <!-- Confirmation du mot de passe -->
        <mat-form-field appearance="fill" class="full-width">
          <mat-label>Confirm Password</mat-label>
          <input matInput [(ngModel)]="ConfirmerPassword" name="ConfirmerPassword" [type]="confirmPasswordType" required>
          <mat-icon matSuffix class="visibility-toggle" (click)="toggleConfirmPasswordVisibility()" [attr.aria-label]="confirmPasswordType === 'password' ? 'Show confirm password' : 'Hide confirm password'">
            {{ confirmPasswordType === 'password' ? 'visibility_off' : 'visibility' }}
          </mat-icon>
          <mat-error *ngIf="confirmPasswordError">Passwords do not match</mat-error>
        </mat-form-field>

        <!-- Champs pour Influenceur -->
        <div *ngIf="clientType === 'influenceur'">
          <mat-form-field appearance="fill" class="full-width">
            <mat-label>TikTok link</mat-label>
            <input matInput [(ngModel)]="tiktokUrl" name="tiktokUrl">
          </mat-form-field>
          <mat-form-field appearance="fill" class="full-width">
            <mat-label>Instagram link</mat-label>
            <input matInput [(ngModel)]="instagramUrl" name="instagramUrl">
          </mat-form-field>
          <mat-form-field appearance="fill" class="full-width">
            <mat-label>YouTube link</mat-label>
            <input matInput [(ngModel)]="youtubeUrl" name="youtubeUrl">
          </mat-form-field>
        </div>

        <!-- Bouton Inscription -->
        <button mat-raised-button color="primary" type="submit" class="full-width register-btn" [disabled]="passwordStrength === 'Very Short' || passwordStrength === 'Very Weak'">Sign up</button>
      </form>

      <!-- Connexion Google (Placeholder) -->
      <div class="google-login" *ngIf="clientType === 'entreprise'">
        <p>Or Register With Google</p>
        <button mat-stroked-button disabled>Register with Google</button>
      </div>

      <!-- Lien vers connexion -->
      <p class="login-link">
        Already have an account? <a [routerLink]="['/login']">Login</a>
      </p>
    </mat-card-content>
  </mat-card>
</div>
<div class="footer-dark">
  <footer>
    <div class="container">
      <div class="row">
        <div class="col-sm-6 col-md-3 item">
          <h3>Services</h3>
          <ul>
            <li><a href="#">Marketing</a></li>
            <li><a href="#">Market analytics</a></li>
            <li><a href="#">AI services</a></li>
          </ul>
        </div>
        <div class="col-sm-6 col-md-3 item">
          <h3>About</h3>
          <ul>
            <li><a href="#">Company</a></li>
            <li><a href="#">Team</a></li>
            <li><a href="#">Collaborations</a></li>
          </ul>
        </div>
        <div class="col-md-6 item text">
          <h3>Buzzmark</h3>
          <p>Web application that connects brands and influencers to create authentic and effective marketing campaigns.</p>
        </div>
        <div class="col item social">
          <a href="https://www.facebook.com/"><i class="icon ion-social-facebook"></i></a>
          <a href="https://x.com/"><i class="icon ion-social-twitter"></i></a>
          <a href="https://www.snapchat.com/web?ref=web_nav_chat_icon"><i class="icon ion-social-snapchat"></i></a>
          <a href="https://www.instagram.com/"><i class="icon ion-social-instagram"></i></a>
        </div>
      </div>
      <p class="copyright">Buzzmark © 2025</p>
    </div>
  </footer>
</div>