.register-container {
       display: flex;
       justify-content: center;
       align-items: center;
       min-height: 100vh;
       background: linear-gradient(135deg, #1e3a8a, #f97316);
       padding: 20px;
     }

     .register-card {
       max-width: 400px;
       width: 100%;
       background: transparent;
       border-radius: 10px;
       box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
       padding: 10px;
       animation: fadeIn 1s ease-in-out;
     }

     .register-form {
       display: flex;
       flex-direction: column;
       gap: 10px;
     }

     .full-width {
       width: 100%;
     }

     /* Specific styling for register form fields with fill appearance */
     .register-form .mat-mdc-form-field {
       margin-bottom: 16px;
     }

     .register-form .mat-mdc-form-field .mdc-text-field {
       background-color: rgba(255, 255, 255, 0.95) !important;
       border-radius: 8px 8px 0 0 !important;
       border: none !important;
     }

     .register-form .mat-mdc-form-field .mdc-text-field--filled {
       background-color: rgba(255, 255, 255, 0.95) !important;
     }

     /* Style the underline for fill appearance */
     .register-form .mat-mdc-form-field .mdc-line-ripple {
       background-color: #1976d2 !important;
     }

     .register-form .mat-mdc-form-field .mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before {
       border-bottom-color: rgba(0, 0, 0, 0.42) !important;
     }

     .register-btn {
      background: transparent;
       padding: 10px;
       font-size: 16px;
       transition: transform 0.3s ease;
       border-radius: 20px;

     }

     .register-btn:hover {
       transform: scale(1.05);
       background: linear-gradient(135deg, #1e3a8a, #f97316);
     }

     .google-login {
       text-align: center;
       margin-top: 20px;
     }

     .login-link {
       text-align: center;
       margin-top: 20px;
       font-size: 14px;
     }

     .login-link a {
       color: #2516f9;
       text-decoration: none;
       font-weight: bold;
     }

     .login-link a:hover {
       text-decoration: underline;
     }

     @keyframes fadeIn {
       from { opacity: 0; transform: translateY(20px); }
       to { opacity: 1; transform: translateY(0); }
     }
