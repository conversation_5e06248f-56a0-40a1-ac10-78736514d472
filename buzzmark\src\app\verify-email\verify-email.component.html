<div class="logo">
  <h1 style="color: #f97316; font-weight: 600; text-align: center;font-family: cursive;font-size: 25px;position:absolute;top: 20px;left:20px;"><a href="/buzzmark/src/app/home/<USER>">Buzzmark</a></h1>
</div>
<!-- ✅ Tailwind CSS & Google Fonts -->
<link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet" />

<!-- ✅ Heroicons (pour l’icône dans le champ) -->
<script src="https://unpkg.com/heroicons@2.0.16/dist/heroicons.min.js"></script>

<div class="min-h-screen flex items-center justify-center bg-orange-50 font-[Poppins] px-4">
  <div class="bg-white shadow-2xl rounded-3xl p-8 w-full max-w-md transition-transform duration-300 hover:scale-105">
    <h2 class="text-3xl font-semibold text-orange-500 text-center mb-2 animate-fade-in">Vérification de l'email</h2>
    <p class="text-gray-600 text-center mb-6">Entrez le code envoyé à votre adresse email</p>

    <form (ngSubmit)="onSubmit()" class="space-y-5">
      <div>
        <label class="block text-sm font-medium text-gray-700 mb-1">Code de vérification</label>
        <div class="relative">
          <input
            [(ngModel)]="code"
            name="code"
            required
            type="text"
            class="w-full px-4 py-2 pl-10 border border-gray-300 rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-orange-400"
          />
          <!-- Icône (clé) -->
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 absolute left-3 top-3.5 text-orange-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 00-8 0v4a4 4 0 008 0V7z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 17v.01" />
          </svg>
        </div>
        <div *ngIf="codeError" class="text-red-500 text-sm mt-1 animate-pulse">Ce champ est requis</div>
      </div>

      <button
        type="submit"
        [disabled]="loading"
        class="w-full bg-orange-500 hover:bg-orange-600 text-white py-2 rounded-xl font-semibold transition duration-200 flex items-center justify-center gap-2"
      >
        <svg *ngIf="loading" class="animate-spin h-5 w-5 text-white" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8H4z"></path>
        </svg>
        <span *ngIf="!loading">Vérifier</span>
        <span *ngIf="loading">Traitement...</span>
      </button>
    </form>

    <div class="text-center mt-5 text-sm text-gray-600">
      Vous n'avez pas reçu le code ?
      <a (click)="resendCode()" class="text-orange-500 font-medium hover:underline cursor-pointer">Renvoyer</a>
    </div>
  </div>
</div>
